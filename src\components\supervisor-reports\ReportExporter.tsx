'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Download, Printer, FileText, Image, File, FileType, FileSpreadsheet } from 'lucide-react';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';
import { toast } from 'react-toastify';

interface ReportExporterProps {
  reportData: {
    title: string;
    description?: string;
    periodStart: Date;
    periodEnd: Date;
    literaryContent: string;
    financialData: any[];
  };
  officeSettings?: {
    organizationName: string;
    officeName: string;
    presidentName: string;
    presidentTitle: string;
    logoUrl?: string;
  };
}

export default function ReportExporter({ reportData, officeSettings }: ReportExporterProps) {
  const [isExporting, setIsExporting] = useState(false);

  // دالة لتنظيف النص من علامات HTML مع الحفاظ على الفقرات
  const cleanHtmlText = (htmlText: string): string => {
    if (!htmlText) return '';

    try {
      // إنشاء عنصر div مؤقت لتحويل HTML إلى نص
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = htmlText;

      // معالجة خاصة للعناصر التي تحتاج إلى أسطر جديدة
      const blockElements = tempDiv.querySelectorAll('p, div, h1, h2, h3, h4, h5, h6, li, br');
      blockElements.forEach(element => {
        if (element.tagName === 'BR') {
          element.replaceWith('\n');
        } else if (element.tagName === 'LI') {
          // إضافة رمز نقطة للعناصر في القائمة
          const textContent = element.textContent || '';
          element.replaceWith(`\n• ${textContent}`);
        } else {
          // إضافة سطر جديد بعد العناصر الكتلية
          const textContent = element.textContent || '';
          element.replaceWith(`${textContent}\n`);
        }
      });

      // الحصول على النص الصافي
      let cleanText = tempDiv.textContent || tempDiv.innerText || '';

      // تنظيف الكيانات HTML
      cleanText = cleanText
        .replace(/&nbsp;/g, ' ') // استبدال &nbsp; بمسافة عادية
        .replace(/&amp;/g, '&') // استبدال &amp; بـ &
        .replace(/&lt;/g, '<') // استبدال &lt; بـ <
        .replace(/&gt;/g, '>') // استبدال &gt; بـ >
        .replace(/&quot;/g, '"') // استبدال &quot; بـ "
        .replace(/&#39;/g, "'"); // استبدال &#39; بـ '

      // تنظيف الأسطر مع الحفاظ على الفقرات
      cleanText = cleanText
        .split('\n')
        .map(line => line.trim()) // إزالة المسافات من بداية ونهاية كل سطر
        .filter(line => line.length > 0) // إزالة الأسطر الفارغة
        .join('\n\n'); // ربط الأسطر بسطرين جديدين لإنشاء فقرات منفصلة

      return cleanText.trim();
    } catch (error) {
      console.error('خطأ في تنظيف النص:', error);
      // في حالة الخطأ، نعيد النص كما هو مع إزالة علامات HTML الأساسية
      return htmlText
        .replace(/<[^>]*>/g, '') // إزالة جميع علامات HTML
        .replace(/&nbsp;/g, ' ')
        .replace(/&amp;/g, '&')
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&quot;/g, '"')
        .replace(/\s+/g, ' ') // تنظيف المسافات المتعددة
        .trim();
    }
  };

  // إنشاء محتوى HTML للتقرير
  const generateReportHTML = () => {
    const { title, description, periodStart, periodEnd, literaryContent, financialData } = reportData;
    
    // حساب المجاميع المالية
    const totalIncome = financialData
      .filter(row => row.type === 'income')
      .reduce((sum, row) => sum + row.amount, 0);
      
    const totalExpenses = financialData
      .filter(row => row.type === 'expense')
      .reduce((sum, row) => sum + row.amount, 0);
      
    const balance = totalIncome - totalExpenses;

    return `
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap');
        
        body {
            font-family: 'Amiri', 'Arial', sans-serif;
            line-height: 1.8;
            margin: 0;
            padding: 20px;
            background: white;
            color: #333;
            font-size: 16px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #2563eb;
            padding-bottom: 30px;
        }
        
        .logo {
            max-width: 120px;
            margin-bottom: 15px;
        }
        
        .organization-name {
            font-size: 28px;
            font-weight: bold;
            color: #1e40af;
            margin-bottom: 8px;
        }
        
        .office-name {
            font-size: 20px;
            color: #374151;
            margin-bottom: 15px;
        }
        
        .report-title {
            font-size: 24px;
            font-weight: bold;
            color: #dc2626;
            margin: 20px 0 10px 0;
        }
        
        .period {
            font-size: 18px;
            color: #6b7280;
            margin-bottom: 10px;
        }
        
        .description {
            font-size: 16px;
            color: #4b5563;
            font-style: italic;
        }
        
        .section {
            margin: 40px 0;
            padding: 25px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            background: #fafafa;
        }
        
        .section-title {
            font-size: 22px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e5e7eb;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .literary-content {
            line-height: 2;
            font-size: 17px;
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #d1d5db;
        }
        
        .financial-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #d1d5db;
        }
        
        .financial-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 16px;
        }
        
        .financial-table th,
        .financial-table td {
            border: 2px solid #d1d5db;
            padding: 12px 15px;
            text-align: center;
        }
        
        .financial-table th {
            background-color: #f3f4f6;
            font-weight: bold;
            font-size: 17px;
        }
        
        .income-header {
            background-color: #d1fae5 !important;
            color: #065f46;
        }
        
        .expense-header {
            background-color: #fee2e2 !important;
            color: #991b1b;
        }
        
        .income-row {
            background-color: #f0fdf4;
        }
        
        .expense-row {
            background-color: #fef2f2;
        }
        
        .total-row {
            background-color: #f9fafb;
            font-weight: bold;
            font-size: 17px;
        }
        
        .balance-positive {
            background-color: #d1fae5 !important;
            color: #065f46;
            font-weight: bold;
            font-size: 18px;
        }
        
        .balance-negative {
            background-color: #fee2e2 !important;
            color: #991b1b;
            font-weight: bold;
            font-size: 18px;
        }
        
        .footer {
            margin-top: 60px;
            text-align: center;
            font-size: 15px;
            color: #6b7280;
            border-top: 2px solid #e5e7eb;
            padding-top: 30px;
        }
        
        .signature-section {
            margin-top: 40px;
            text-align: left;
            font-size: 16px;
        }
        
        @media print {
            body { 
                margin: 0; 
                padding: 15px; 
                font-size: 14px;
            }
            .section { 
                break-inside: avoid; 
                margin: 20px 0;
                padding: 15px;
            }
            .financial-table {
                font-size: 14px;
            }
            .financial-table th,
            .financial-table td {
                padding: 8px 10px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        ${officeSettings?.logoUrl ? `<img src="${officeSettings.logoUrl}" alt="شعار الجمعية" class="logo">` : ''}
        <div class="organization-name">${officeSettings?.organizationName || 'جمعـية العـلمـاء المسلـميـن الجـزائـرييــــــن'}</div>
        <div class="office-name">${officeSettings?.officeName || 'المكـــــــتب البلدي'}</div>
        <div class="report-title">${title}</div>
        <div class="period">
            الفترة: من ${format(periodStart, 'PPP', { locale: ar })} إلى ${format(periodEnd, 'PPP', { locale: ar })}
        </div>
        ${description ? `<div class="description">${description}</div>` : ''}
    </div>

    ${literaryContent ? `
    <div class="section">
        <div class="section-title">
            📘 التقرير الأدبي
        </div>
        <div class="literary-content">
            ${literaryContent}
        </div>
    </div>
    ` : ''}

    ${financialData && financialData.length > 0 ? `
    <div class="section">
        <div class="section-title">
            💰 التقرير المالي
        </div>
        <div class="financial-section">
            <table class="financial-table">
                <thead>
                    <tr>
                        <th class="income-header">المداخيل</th>
                        <th class="income-header">المبلغ (دج)</th>
                        <th class="expense-header">المصاريف</th>
                        <th class="expense-header">المبلغ (دج)</th>
                    </tr>
                </thead>
                <tbody>
                    ${generateFinancialTableRows(financialData)}
                </tbody>
                <tfoot>
                    <tr class="total-row">
                        <td>إجمالي المداخيل</td>
                        <td>${totalIncome.toLocaleString()}</td>
                        <td>إجمالي المصاريف</td>
                        <td>${totalExpenses.toLocaleString()}</td>
                    </tr>
                    <tr class="${balance >= 0 ? 'balance-positive' : 'balance-negative'}">
                        <td colspan="3">الرصيد النهائي</td>
                        <td>${balance.toLocaleString()}</td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
    ` : ''}

    <div class="footer">
        <p>تم إنشاء هذا التقرير في: ${format(new Date(), 'PPP', { locale: ar })}</p>
        <div class="signature-section">
            <p>${officeSettings?.presidentName || ''}</p>
            <p>${officeSettings?.presidentTitle || ''}</p>
        </div>
    </div>
</body>
</html>
    `;
  };

  // إنشاء صفوف الجدول المالي
  const generateFinancialTableRows = (data: any[]) => {
    const incomeRows = data.filter(row => row.type === 'income');
    const expenseRows = data.filter(row => row.type === 'expense');
    const maxRows = Math.max(incomeRows.length, expenseRows.length);
    
    let rows = '';
    for (let i = 0; i < maxRows; i++) {
      const income = incomeRows[i];
      const expense = expenseRows[i];
      
      rows += `
        <tr>
          <td class="income-row">${income ? income.description : ''}</td>
          <td class="income-row">${income ? income.amount.toLocaleString() : ''}</td>
          <td class="expense-row">${expense ? expense.description : ''}</td>
          <td class="expense-row">${expense ? expense.amount.toLocaleString() : ''}</td>
        </tr>
      `;
    }
    
    return rows;
  };

  // معاينة التقرير
  const handlePreview = () => {
    const previewWindow = window.open('', '_blank', 'width=1200,height=800');
    if (previewWindow) {
      previewWindow.document.open();
      previewWindow.document.write(generateReportHTML());
      previewWindow.document.close();
    }
  };

  // طباعة التقرير باستخدام الطريقة الناجحة
  const handlePrint = () => {
    try {
      const printContent = generateReportHTML();
      const printWindow = window.open('', '_blank');
      if (printWindow) {
        printWindow.document.open();
        printWindow.document.write(printContent);
        printWindow.document.close();

        // انتظار تحميل المحتوى قبل الطباعة
        printWindow.onload = () => {
          printWindow.print();
          printWindow.close();
          toast.success('تم إرسال التقرير للطباعة بنجاح');
        };

        // في حالة عدم تشغيل onload
        setTimeout(() => {
          if (!printWindow.closed) {
            printWindow.print();
          }
        }, 500);
      } else {
        toast.error('تعذر فتح نافذة الطباعة. تأكد من السماح للنوافذ المنبثقة.');
      }
    } catch (error) {
      console.error('خطأ في الطباعة:', error);
      toast.error('حدث خطأ أثناء الطباعة');
    }
  };

  // تصدير إلى HTML
  const handleExportHTML = () => {
    try {
      const htmlContent = generateReportHTML();
      const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${reportData.title}_${format(new Date(), 'yyyy-MM-dd')}.html`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      toast.success('تم تصدير التقرير بصيغة HTML بنجاح');
    } catch (error) {
      console.error('خطأ في تصدير HTML:', error);
      toast.error('حدث خطأ أثناء تصدير HTML');
    }
  };

  // تصدير إلى PDF باستخدام الطريقة الناجحة
  const handleExportPDF = async () => {
    setIsExporting(true);
    try {
      // استخدام دالة exportToPdf الناجحة من utils/export-utils.ts
      const { exportToPdf } = await import('@/utils/export-utils');

      // تحضير بيانات التقرير للتصدير
      const reportTables = [];

      // إضافة معلومات التقرير الأساسية
      const basicInfo = [
        ['عنوان التقرير', reportData.title],
        ['الوصف', reportData.description || 'غير محدد'],
        ['فترة التقرير', `من ${format(reportData.periodStart, 'PPP', { locale: ar })} إلى ${format(reportData.periodEnd, 'PPP', { locale: ar })}`],
        ['تاريخ الإنشاء', format(new Date(), 'PPP', { locale: ar })]
      ];

      reportTables.push({
        title: 'معلومات التقرير',
        headers: ['البيان', 'القيمة'],
        data: basicInfo,
        headStyles: {
          fillColor: [59, 130, 246],
          textColor: [255, 255, 255]
        }
      });

      // إضافة المحتوى الأدبي إذا كان موجوداً
      if (reportData.literaryContent && reportData.literaryContent.trim()) {
        const cleanedLiteraryContent = cleanHtmlText(reportData.literaryContent);
        const literaryData = [
          ['المحتوى الأدبي', cleanedLiteraryContent]
        ];

        reportTables.push({
          title: 'التقرير الأدبي',
          headers: ['البيان', 'المحتوى'],
          data: literaryData,
          headStyles: {
            fillColor: [139, 69, 19],
            textColor: [255, 255, 255]
          }
        });
      }

      // إضافة البيانات المالية إذا كانت موجودة
      if (reportData.financialData && reportData.financialData.length > 0) {
        const financialHeaders = Object.keys(reportData.financialData[0]);
        const financialRows = reportData.financialData.map(item =>
          Object.values(item).map(value =>
            typeof value === 'number' ? value.toLocaleString('fr-FR') + ' د.ج' : String(value)
          )
        );

        reportTables.push({
          title: 'البيانات المالية',
          headers: financialHeaders,
          data: financialRows,
          headStyles: {
            fillColor: [22, 155, 136],
            textColor: [255, 255, 255]
          }
        });
      }

      // تصدير PDF
      exportToPdf({
        title: `${officeSettings?.organizationName || 'جمعية العلماء المسلمين الجزائريين'}\n${officeSettings?.officeName || 'المكتب البلدي'}\n${reportData.title}`,
        fileName: `${reportData.title}_${format(new Date(), 'yyyy-MM-dd')}.pdf`,
        tables: reportTables,
        schoolInfo: {
          name: officeSettings?.organizationName || 'جمعية العلماء المسلمين الجزائريين',
          address: officeSettings?.officeName || 'المكتب البلدي'
        }
      });

    } catch (error) {
      console.error('خطأ في تصدير PDF:', error);
      toast.error('حدث خطأ أثناء تصدير PDF');
    } finally {
      setIsExporting(false);
    }
  };

  // تصدير إلى Word باستخدام مكتبة docx
  const handleExportWord = async () => {
    setIsExporting(true);
    try {
      // استيراد المكتبات المطلوبة
      const { Document, Packer, Paragraph, TextRun, HeadingLevel, Table, TableRow, TableCell, WidthType, AlignmentType } = await import('docx');

      // إنشاء مستند Word جديد
      const doc = new Document({
        sections: [
          {
            properties: {},
            children: [
              // العنوان الرئيسي
              new Paragraph({
                children: [
                  new TextRun({
                    text: officeSettings?.organizationName || 'جمعية العلماء المسلمين الجزائريين',
                    bold: true,
                    size: 32,
                  }),
                ],
                heading: HeadingLevel.TITLE,
                alignment: AlignmentType.CENTER,
              }),

              // اسم المكتب
              new Paragraph({
                children: [
                  new TextRun({
                    text: officeSettings?.officeName || 'المكتب البلدي',
                    bold: true,
                    size: 24,
                  }),
                ],
                alignment: AlignmentType.CENTER,
              }),

              // عنوان التقرير
              new Paragraph({
                children: [
                  new TextRun({
                    text: reportData.title,
                    bold: true,
                    size: 28,
                    color: "2563eb",
                  }),
                ],
                heading: HeadingLevel.HEADING_1,
                alignment: AlignmentType.CENTER,
              }),

              // فترة التقرير
              new Paragraph({
                children: [
                  new TextRun({
                    text: `فترة التقرير: من ${format(reportData.periodStart, 'PPP', { locale: ar })} إلى ${format(reportData.periodEnd, 'PPP', { locale: ar })}`,
                    size: 20,
                  }),
                ],
                alignment: AlignmentType.CENTER,
              }),

              // الوصف إذا كان موجوداً
              ...(reportData.description ? [
                new Paragraph({
                  children: [
                    new TextRun({
                      text: "وصف التقرير:",
                      bold: true,
                      size: 22,
                    }),
                  ],
                  heading: HeadingLevel.HEADING_2,
                }),
                new Paragraph({
                  children: [
                    new TextRun({
                      text: reportData.description,
                      size: 20,
                    }),
                  ],
                }),
              ] : []),

              // المحتوى الأدبي إذا كان موجوداً
              ...(reportData.literaryContent && reportData.literaryContent.trim() ? [
                new Paragraph({
                  children: [
                    new TextRun({
                      text: "التقرير الأدبي",
                      bold: true,
                      size: 24,
                      color: "8b4513",
                    }),
                  ],
                  heading: HeadingLevel.HEADING_2,
                }),
                // تنظيف النص من علامات HTML وتقسيمه إلى فقرات
                ...cleanHtmlText(reportData.literaryContent).split('\n\n').map(paragraph =>
                  new Paragraph({
                    children: [
                      new TextRun({
                        text: paragraph.trim(),
                        size: 20,
                      }),
                    ],
                    spacing: {
                      after: 200, // مسافة بعد كل فقرة
                    },
                  })
                ),
              ] : []),

              // البيانات المالية إذا كانت موجودة
              ...(reportData.financialData && reportData.financialData.length > 0 ? [
                new Paragraph({
                  children: [
                    new TextRun({
                      text: "البيانات المالية",
                      bold: true,
                      size: 24,
                      color: "059669",
                    }),
                  ],
                  heading: HeadingLevel.HEADING_2,
                }),
                new Table({
                  width: {
                    size: 100,
                    type: WidthType.PERCENTAGE,
                  },
                  rows: [
                    // رأس الجدول
                    new TableRow({
                      children: Object.keys(reportData.financialData[0]).map(header =>
                        new TableCell({
                          children: [
                            new Paragraph({
                              children: [
                                new TextRun({
                                  text: header,
                                  bold: true,
                                  color: "ffffff",
                                }),
                              ],
                              alignment: AlignmentType.CENTER,
                            }),
                          ],
                          shading: {
                            fill: "059669",
                          },
                        })
                      ),
                    }),
                    // بيانات الجدول
                    ...reportData.financialData.map(row =>
                      new TableRow({
                        children: Object.values(row).map(value =>
                          new TableCell({
                            children: [
                              new Paragraph({
                                children: [
                                  new TextRun({
                                    text: typeof value === 'number' ? value.toLocaleString('fr-FR') + ' د.ج' : String(value),
                                  }),
                                ],
                                alignment: AlignmentType.CENTER,
                              }),
                            ],
                          })
                        ),
                      })
                    ),
                  ],
                }),
              ] : []),

              // تاريخ الإنشاء
              new Paragraph({
                children: [
                  new TextRun({
                    text: `تاريخ إنشاء التقرير: ${format(new Date(), 'PPP', { locale: ar })}`,
                    size: 18,
                    italics: true,
                  }),
                ],
                alignment: AlignmentType.LEFT,
              }),
            ],
          },
        ],
      });

      // تحويل المستند إلى buffer
      const buffer = await Packer.toBuffer(doc);

      // إنشاء blob وتنزيل الملف
      const blob = new Blob([buffer], {
        type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      });

      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${reportData.title}_${format(new Date(), 'yyyy-MM-dd')}.docx`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast.success('تم تصدير التقرير بصيغة Word بنجاح');

    } catch (error) {
      console.error('خطأ في تصدير Word:', error);
      toast.error('حدث خطأ أثناء تصدير Word');
    } finally {
      setIsExporting(false);
    }
  };

  // تصدير إلى Excel باستخدام مكتبة xlsx
  const handleExportExcel = async () => {
    setIsExporting(true);
    try {
      // استيراد مكتبة xlsx
      const XLSX = await import('xlsx');

      // إنشاء مصنف Excel جديد
      const workbook = XLSX.utils.book_new();

      // ورقة المعلومات الأساسية
      const basicInfoData = [
        ['معلومات التقرير'],
        [''],
        ['البيان', 'القيمة'],
        ['عنوان التقرير', reportData.title],
        ['الوصف', reportData.description || 'غير محدد'],
        ['تاريخ البداية', format(reportData.periodStart, 'PPP', { locale: ar })],
        ['تاريخ النهاية', format(reportData.periodEnd, 'PPP', { locale: ar })],
        ['تاريخ الإنشاء', format(new Date(), 'PPP', { locale: ar })],
        ['المؤسسة', officeSettings?.organizationName || 'جمعية العلماء المسلمين الجزائريين'],
        ['المكتب', officeSettings?.officeName || 'المكتب البلدي'],
      ];

      const basicInfoSheet = XLSX.utils.aoa_to_sheet(basicInfoData);

      // تنسيق ورقة المعلومات الأساسية
      basicInfoSheet['!cols'] = [{ wch: 20 }, { wch: 40 }];

      // إضافة ورقة المعلومات الأساسية
      XLSX.utils.book_append_sheet(workbook, basicInfoSheet, 'معلومات التقرير');

      // ورقة المحتوى الأدبي إذا كان موجوداً
      if (reportData.literaryContent && reportData.literaryContent.trim()) {
        const cleanedLiteraryContent = cleanHtmlText(reportData.literaryContent);
        const literaryLines = cleanedLiteraryContent.split('\n').filter(line => line.trim());
        const literaryData = [
          ['المحتوى الأدبي'],
          [''],
          ['رقم السطر', 'المحتوى'],
          ...literaryLines.map((line, index) => [index + 1, line.trim()])
        ];

        const literarySheet = XLSX.utils.aoa_to_sheet(literaryData);
        literarySheet['!cols'] = [{ wch: 10 }, { wch: 80 }];

        XLSX.utils.book_append_sheet(workbook, literarySheet, 'المحتوى الأدبي');
      }

      // ورقة البيانات المالية إذا كانت موجودة
      if (reportData.financialData && reportData.financialData.length > 0) {
        // تحضير بيانات الجدول المالي
        const financialHeaders = Object.keys(reportData.financialData[0]);
        const financialRows = reportData.financialData.map(item =>
          Object.values(item).map(value => {
            if (typeof value === 'number') {
              return value; // الاحتفاظ بالأرقام كأرقام في Excel
            }
            return String(value);
          })
        );

        const financialData = [
          ['البيانات المالية'],
          [''],
          financialHeaders,
          ...financialRows
        ];

        const financialSheet = XLSX.utils.aoa_to_sheet(financialData);

        // تنسيق أعمدة البيانات المالية
        const colWidths = financialHeaders.map(header => {
          if (header.includes('مبلغ') || header.includes('amount')) {
            return { wch: 15 };
          }
          return { wch: 20 };
        });
        financialSheet['!cols'] = colWidths;

        XLSX.utils.book_append_sheet(workbook, financialSheet, 'البيانات المالية');

        // ورقة ملخص مالي
        const totalIncome = reportData.financialData
          .filter(item => item.type === 'دخل' || item.type === 'income')
          .reduce((sum, item) => sum + (typeof item.amount === 'number' ? item.amount : 0), 0);

        const totalExpense = reportData.financialData
          .filter(item => item.type === 'مصروف' || item.type === 'expense')
          .reduce((sum, item) => sum + (typeof item.amount === 'number' ? item.amount : 0), 0);

        const netBalance = totalIncome - totalExpense;

        const summaryData = [
          ['الملخص المالي'],
          [''],
          ['البيان', 'المبلغ (د.ج)', 'النسبة'],
          ['إجمالي المداخيل', totalIncome, '100%'],
          ['إجمالي المصروفات', totalExpense, totalIncome > 0 ? `${((totalExpense / totalIncome) * 100).toFixed(1)}%` : '0%'],
          ['الرصيد الصافي', netBalance, netBalance >= 0 ? 'فائض' : 'عجز'],
          [''],
          ['تحليل إضافي'],
          ['عدد العمليات المالية', reportData.financialData.length],
          ['متوسط قيمة العملية', reportData.financialData.length > 0 ? Math.round((totalIncome + totalExpense) / reportData.financialData.length) : 0],
        ];

        const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
        summarySheet['!cols'] = [{ wch: 25 }, { wch: 20 }, { wch: 15 }];

        XLSX.utils.book_append_sheet(workbook, summarySheet, 'الملخص المالي');
      }

      // تحويل المصنف إلى buffer
      const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });

      // إنشاء blob وتنزيل الملف
      const blob = new Blob([excelBuffer], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      });

      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${reportData.title}_${format(new Date(), 'yyyy-MM-dd')}.xlsx`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast.success('تم تصدير التقرير بصيغة Excel بنجاح');

    } catch (error) {
      console.error('خطأ في تصدير Excel:', error);
      toast.error('حدث خطأ أثناء تصدير Excel');
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Download className="h-5 w-5 text-blue-600" />
          🖨️ طباعة وتصدير التقرير
        </CardTitle>
        <CardDescription>
          خيارات متعددة لطباعة وتصدير التقرير بصيغ مختلفة
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* معاينة سريعة */}
        <div className="grid md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <h4 className="font-medium">معلومات التقرير:</h4>
            <div className="text-sm space-y-1">
              <p><strong>العنوان:</strong> {reportData.title}</p>
              <p><strong>الفترة:</strong> {format(reportData.periodStart, 'PPP', { locale: ar })} - {format(reportData.periodEnd, 'PPP', { locale: ar })}</p>
              <div className="flex items-center gap-2">
                <Badge variant={reportData.literaryContent ? 'default' : 'secondary'}>
                  {reportData.literaryContent ? '✅' : '❌'} تقرير أدبي
                </Badge>
                <Badge variant={reportData.financialData.length > 0 ? 'default' : 'secondary'}>
                  {reportData.financialData.length > 0 ? '✅' : '❌'} تقرير مالي
                </Badge>
              </div>
            </div>
          </div>
          
          <div className="space-y-2">
            <h4 className="font-medium">إحصائيات سريعة:</h4>
            <div className="text-sm space-y-1">
              <p><strong>عدد البنود المالية:</strong> {reportData.financialData.length}</p>
              <p><strong>حجم المحتوى الأدبي:</strong> {reportData.literaryContent.length} حرف</p>
            </div>
          </div>
        </div>

        <Separator />

        {/* أزرار الطباعة والتصدير */}
        <div className="space-y-4">
          <h4 className="font-medium">خيارات الطباعة والتصدير:</h4>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-3">
            <Button
              onClick={handlePreview}
              variant="outline"
              className="flex items-center gap-2 h-auto p-4 flex-col"
            >
              <FileText className="h-6 w-6 text-blue-600" />
              <span className="text-sm">معاينة</span>
            </Button>
            
            <Button
              onClick={handlePrint}
              variant="outline"
              className="flex items-center gap-2 h-auto p-4 flex-col"
            >
              <Printer className="h-6 w-6 text-gray-600" />
              <span className="text-sm">طباعة</span>
            </Button>
            
            <Button
              onClick={handleExportHTML}
              variant="outline"
              className="flex items-center gap-2 h-auto p-4 flex-col"
            >
              <File className="h-6 w-6 text-orange-600" />
              <span className="text-sm">تصدير HTML</span>
            </Button>

            <Button
              onClick={handleExportWord}
              disabled={isExporting}
              variant="outline"
              className="flex items-center gap-2 h-auto p-4 flex-col"
            >
              <FileType className="h-6 w-6 text-blue-800" />
              <span className="text-sm">
                {isExporting ? 'جاري التصدير...' : 'تصدير Word'}
              </span>
            </Button>

            <Button
              onClick={handleExportExcel}
              disabled={isExporting}
              variant="outline"
              className="flex items-center gap-2 h-auto p-4 flex-col"
            >
              <FileSpreadsheet className="h-6 w-6 text-green-600" />
              <span className="text-sm">
                {isExporting ? 'جاري التصدير...' : 'تصدير Excel'}
              </span>
            </Button>
            
            <Button
              onClick={handleExportPDF}
              disabled={isExporting}
              variant="outline"
              className="flex items-center gap-2 h-auto p-4 flex-col"
            >
              <Download className="h-6 w-6 text-red-600" />
              <span className="text-sm">
                {isExporting ? 'جاري التصدير...' : 'تصدير PDF'}
              </span>
            </Button>
          </div>
        </div>

        {/* معلومات إضافية */}
        <div className="text-sm text-gray-500 space-y-1">
          <p>💡 نصائح:</p>
          <ul className="list-disc list-inside space-y-1 text-xs">
            <li>استخدم "معاينة" لرؤية التقرير قبل الطباعة</li>
            <li>تصدير HTML يحافظ على التنسيق الكامل</li>
            <li>تصدير Word مثالي للتعديل والتخصيص</li>
            <li>تصدير Excel مثالي لتحليل البيانات المالية</li>
            <li>الطباعة تستخدم تنسيق محسن للورق</li>
            <li>يمكنك حفظ HTML وتحويله لاحقاً إلى PDF</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}
