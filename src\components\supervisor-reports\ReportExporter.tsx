'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Download, Printer, FileText, Image, File } from 'lucide-react';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';

interface ReportExporterProps {
  reportData: {
    title: string;
    description?: string;
    periodStart: Date;
    periodEnd: Date;
    literaryContent: string;
    financialData: any[];
  };
  officeSettings?: {
    organizationName: string;
    officeName: string;
    presidentName: string;
    presidentTitle: string;
    logoUrl?: string;
  };
}

export default function ReportExporter({ reportData, officeSettings }: ReportExporterProps) {
  const [isExporting, setIsExporting] = useState(false);

  // إنشاء محتوى HTML للتقرير
  const generateReportHTML = () => {
    const { title, description, periodStart, periodEnd, literaryContent, financialData } = reportData;
    
    // حساب المجاميع المالية
    const totalIncome = financialData
      .filter(row => row.type === 'income')
      .reduce((sum, row) => sum + row.amount, 0);
      
    const totalExpenses = financialData
      .filter(row => row.type === 'expense')
      .reduce((sum, row) => sum + row.amount, 0);
      
    const balance = totalIncome - totalExpenses;

    return `
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap');
        
        body {
            font-family: 'Amiri', 'Arial', sans-serif;
            line-height: 1.8;
            margin: 0;
            padding: 20px;
            background: white;
            color: #333;
            font-size: 16px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #2563eb;
            padding-bottom: 30px;
        }
        
        .logo {
            max-width: 120px;
            margin-bottom: 15px;
        }
        
        .organization-name {
            font-size: 28px;
            font-weight: bold;
            color: #1e40af;
            margin-bottom: 8px;
        }
        
        .office-name {
            font-size: 20px;
            color: #374151;
            margin-bottom: 15px;
        }
        
        .report-title {
            font-size: 24px;
            font-weight: bold;
            color: #dc2626;
            margin: 20px 0 10px 0;
        }
        
        .period {
            font-size: 18px;
            color: #6b7280;
            margin-bottom: 10px;
        }
        
        .description {
            font-size: 16px;
            color: #4b5563;
            font-style: italic;
        }
        
        .section {
            margin: 40px 0;
            padding: 25px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            background: #fafafa;
        }
        
        .section-title {
            font-size: 22px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e5e7eb;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .literary-content {
            line-height: 2;
            font-size: 17px;
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #d1d5db;
        }
        
        .financial-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #d1d5db;
        }
        
        .financial-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 16px;
        }
        
        .financial-table th,
        .financial-table td {
            border: 2px solid #d1d5db;
            padding: 12px 15px;
            text-align: center;
        }
        
        .financial-table th {
            background-color: #f3f4f6;
            font-weight: bold;
            font-size: 17px;
        }
        
        .income-header {
            background-color: #d1fae5 !important;
            color: #065f46;
        }
        
        .expense-header {
            background-color: #fee2e2 !important;
            color: #991b1b;
        }
        
        .income-row {
            background-color: #f0fdf4;
        }
        
        .expense-row {
            background-color: #fef2f2;
        }
        
        .total-row {
            background-color: #f9fafb;
            font-weight: bold;
            font-size: 17px;
        }
        
        .balance-positive {
            background-color: #d1fae5 !important;
            color: #065f46;
            font-weight: bold;
            font-size: 18px;
        }
        
        .balance-negative {
            background-color: #fee2e2 !important;
            color: #991b1b;
            font-weight: bold;
            font-size: 18px;
        }
        
        .footer {
            margin-top: 60px;
            text-align: center;
            font-size: 15px;
            color: #6b7280;
            border-top: 2px solid #e5e7eb;
            padding-top: 30px;
        }
        
        .signature-section {
            margin-top: 40px;
            text-align: left;
            font-size: 16px;
        }
        
        @media print {
            body { 
                margin: 0; 
                padding: 15px; 
                font-size: 14px;
            }
            .section { 
                break-inside: avoid; 
                margin: 20px 0;
                padding: 15px;
            }
            .financial-table {
                font-size: 14px;
            }
            .financial-table th,
            .financial-table td {
                padding: 8px 10px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        ${officeSettings?.logoUrl ? `<img src="${officeSettings.logoUrl}" alt="شعار الجمعية" class="logo">` : ''}
        <div class="organization-name">${officeSettings?.organizationName || 'جمعـية العـلمـاء المسلـميـن الجـزائـرييــــــن'}</div>
        <div class="office-name">${officeSettings?.officeName || 'المكـــــــتب البلدي'}</div>
        <div class="report-title">${title}</div>
        <div class="period">
            الفترة: من ${format(periodStart, 'PPP', { locale: ar })} إلى ${format(periodEnd, 'PPP', { locale: ar })}
        </div>
        ${description ? `<div class="description">${description}</div>` : ''}
    </div>

    ${literaryContent ? `
    <div class="section">
        <div class="section-title">
            📘 التقرير الأدبي
        </div>
        <div class="literary-content">
            ${literaryContent}
        </div>
    </div>
    ` : ''}

    ${financialData && financialData.length > 0 ? `
    <div class="section">
        <div class="section-title">
            💰 التقرير المالي
        </div>
        <div class="financial-section">
            <table class="financial-table">
                <thead>
                    <tr>
                        <th class="income-header">المداخيل</th>
                        <th class="income-header">المبلغ (دج)</th>
                        <th class="expense-header">المصاريف</th>
                        <th class="expense-header">المبلغ (دج)</th>
                    </tr>
                </thead>
                <tbody>
                    ${generateFinancialTableRows(financialData)}
                </tbody>
                <tfoot>
                    <tr class="total-row">
                        <td>إجمالي المداخيل</td>
                        <td>${totalIncome.toLocaleString()}</td>
                        <td>إجمالي المصاريف</td>
                        <td>${totalExpenses.toLocaleString()}</td>
                    </tr>
                    <tr class="${balance >= 0 ? 'balance-positive' : 'balance-negative'}">
                        <td colspan="3">الرصيد النهائي</td>
                        <td>${balance.toLocaleString()}</td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
    ` : ''}

    <div class="footer">
        <p>تم إنشاء هذا التقرير في: ${format(new Date(), 'PPP', { locale: ar })}</p>
        <div class="signature-section">
            <p>${officeSettings?.presidentName || ''}</p>
            <p>${officeSettings?.presidentTitle || ''}</p>
        </div>
    </div>
</body>
</html>
    `;
  };

  // إنشاء صفوف الجدول المالي
  const generateFinancialTableRows = (data: any[]) => {
    const incomeRows = data.filter(row => row.type === 'income');
    const expenseRows = data.filter(row => row.type === 'expense');
    const maxRows = Math.max(incomeRows.length, expenseRows.length);
    
    let rows = '';
    for (let i = 0; i < maxRows; i++) {
      const income = incomeRows[i];
      const expense = expenseRows[i];
      
      rows += `
        <tr>
          <td class="income-row">${income ? income.description : ''}</td>
          <td class="income-row">${income ? income.amount.toLocaleString() : ''}</td>
          <td class="expense-row">${expense ? expense.description : ''}</td>
          <td class="expense-row">${expense ? expense.amount.toLocaleString() : ''}</td>
        </tr>
      `;
    }
    
    return rows;
  };

  // معاينة التقرير
  const handlePreview = () => {
    const previewWindow = window.open('', '_blank', 'width=1200,height=800');
    if (previewWindow) {
      previewWindow.document.open();
      previewWindow.document.write(generateReportHTML());
      previewWindow.document.close();
    }
  };

  // طباعة التقرير
  const handlePrint = () => {
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.open();
      printWindow.document.write(generateReportHTML());
      printWindow.document.close();
      printWindow.onload = () => {
        printWindow.print();
        printWindow.close();
      };
    }
  };

  // تصدير إلى HTML
  const handleExportHTML = () => {
    const htmlContent = generateReportHTML();
    const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${reportData.title}_${format(new Date(), 'yyyy-MM-dd')}.html`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // تصدير إلى PDF (يتطلب مكتبة خارجية في التطبيق الحقيقي)
  const handleExportPDF = async () => {
    setIsExporting(true);
    try {
      // في التطبيق الحقيقي، يمكن استخدام مكتبة مثل jsPDF أو puppeteer
      // هنا سنقوم بتصدير HTML كبديل
      handleExportHTML();
    } catch (error) {
      console.error('خطأ في تصدير PDF:', error);
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Download className="h-5 w-5 text-blue-600" />
          🖨️ طباعة وتصدير التقرير
        </CardTitle>
        <CardDescription>
          خيارات متعددة لطباعة وتصدير التقرير بصيغ مختلفة
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* معاينة سريعة */}
        <div className="grid md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <h4 className="font-medium">معلومات التقرير:</h4>
            <div className="text-sm space-y-1">
              <p><strong>العنوان:</strong> {reportData.title}</p>
              <p><strong>الفترة:</strong> {format(reportData.periodStart, 'PPP', { locale: ar })} - {format(reportData.periodEnd, 'PPP', { locale: ar })}</p>
              <div className="flex items-center gap-2">
                <Badge variant={reportData.literaryContent ? 'default' : 'secondary'}>
                  {reportData.literaryContent ? '✅' : '❌'} تقرير أدبي
                </Badge>
                <Badge variant={reportData.financialData.length > 0 ? 'default' : 'secondary'}>
                  {reportData.financialData.length > 0 ? '✅' : '❌'} تقرير مالي
                </Badge>
              </div>
            </div>
          </div>
          
          <div className="space-y-2">
            <h4 className="font-medium">إحصائيات سريعة:</h4>
            <div className="text-sm space-y-1">
              <p><strong>عدد البنود المالية:</strong> {reportData.financialData.length}</p>
              <p><strong>حجم المحتوى الأدبي:</strong> {reportData.literaryContent.length} حرف</p>
            </div>
          </div>
        </div>

        <Separator />

        {/* أزرار الطباعة والتصدير */}
        <div className="space-y-4">
          <h4 className="font-medium">خيارات الطباعة والتصدير:</h4>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-3">
            <Button
              onClick={handlePreview}
              variant="outline"
              className="flex items-center gap-2 h-auto p-4 flex-col"
            >
              <FileText className="h-6 w-6 text-blue-600" />
              <span className="text-sm">معاينة</span>
            </Button>
            
            <Button
              onClick={handlePrint}
              variant="outline"
              className="flex items-center gap-2 h-auto p-4 flex-col"
            >
              <Printer className="h-6 w-6 text-gray-600" />
              <span className="text-sm">طباعة</span>
            </Button>
            
            <Button
              onClick={handleExportHTML}
              variant="outline"
              className="flex items-center gap-2 h-auto p-4 flex-col"
            >
              <File className="h-6 w-6 text-orange-600" />
              <span className="text-sm">تصدير HTML</span>
            </Button>
            
            <Button
              onClick={handleExportPDF}
              disabled={isExporting}
              variant="outline"
              className="flex items-center gap-2 h-auto p-4 flex-col"
            >
              <Download className="h-6 w-6 text-red-600" />
              <span className="text-sm">
                {isExporting ? 'جاري التصدير...' : 'تصدير PDF'}
              </span>
            </Button>
          </div>
        </div>

        {/* معلومات إضافية */}
        <div className="text-sm text-gray-500 space-y-1">
          <p>💡 نصائح:</p>
          <ul className="list-disc list-inside space-y-1 text-xs">
            <li>استخدم "معاينة" لرؤية التقرير قبل الطباعة</li>
            <li>تصدير HTML يحافظ على التنسيق الكامل</li>
            <li>الطباعة تستخدم تنسيق محسن للورق</li>
            <li>يمكنك حفظ HTML وتحويله لاحقاً إلى PDF</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}
