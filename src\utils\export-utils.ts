import * as XLSX from 'xlsx';
import { jsPDF } from 'jspdf';
import { toast } from 'react-toastify';
// استيراد الأنواع فقط من jspdf-autotable
import type { UserOptions } from 'jspdf-autotable';
// استيراد html2canvas بشكل مباشر
import html2canvas from 'html2canvas';
// استيراد الأنواع والوظائف من نظام التوصيات
import {
  Recommendation,
  generateStudentRecommendations,
  generateClassRecommendations,
  analyzeExamAndGenerateRecommendations,
  formatRecommendations
} from '@/utils/recommendations-utils';

// تعريف نوع موسع لـ jsPDF لتجنب استخدام any
export interface ExtendedJsPDF extends jsPDF {
  lastAutoTable?: {
    finalY: number;
  };
}

/**
 * دالة لتنظيف النص من علامات HTML مع الحفاظ على الفقرات
 * @param htmlText النص المحتوي على علامات HTML
 * @returns النص الصافي بدون علامات HTML مع فقرات منفصلة
 */
export const cleanHtmlText = (htmlText: string): string => {
  if (!htmlText) return '';

  try {
    // إنشاء عنصر div مؤقت لتحويل HTML إلى نص
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = htmlText;

    // معالجة خاصة للعناصر التي تحتاج إلى أسطر جديدة
    const blockElements = tempDiv.querySelectorAll('p, div, h1, h2, h3, h4, h5, h6, li, br');
    blockElements.forEach(element => {
      if (element.tagName === 'BR') {
        element.replaceWith('\n');
      } else if (element.tagName === 'LI') {
        // إضافة رمز نقطة للعناصر في القائمة
        const textContent = element.textContent || '';
        element.replaceWith(`\n• ${textContent}`);
      } else {
        // إضافة سطر جديد بعد العناصر الكتلية
        const textContent = element.textContent || '';
        element.replaceWith(`${textContent}\n`);
      }
    });

    // الحصول على النص الصافي
    let cleanText = tempDiv.textContent || tempDiv.innerText || '';

    // تنظيف الكيانات HTML
    cleanText = cleanText
      .replace(/&nbsp;/g, ' ') // استبدال &nbsp; بمسافة عادية
      .replace(/&amp;/g, '&') // استبدال &amp; بـ &
      .replace(/&lt;/g, '<') // استبدال &lt; بـ <
      .replace(/&gt;/g, '>') // استبدال &gt; بـ >
      .replace(/&quot;/g, '"') // استبدال &quot; بـ "
      .replace(/&#39;/g, "'"); // استبدال &#39; بـ '

    // تنظيف الأسطر مع الحفاظ على الفقرات
    cleanText = cleanText
      .split('\n')
      .map(line => line.trim()) // إزالة المسافات من بداية ونهاية كل سطر
      .filter(line => line.length > 0) // إزالة الأسطر الفارغة
      .join('\n\n'); // ربط الأسطر بسطرين جديدين لإنشاء فقرات منفصلة

    return cleanText.trim();
  } catch (error) {
    console.error('خطأ في تنظيف النص:', error);
    // في حالة الخطأ، نعيد النص كما هو مع إزالة علامات HTML الأساسية
    return htmlText
      .replace(/<[^>]*>/g, '') // إزالة جميع علامات HTML
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/\s+/g, ' ') // تنظيف المسافات المتعددة
      .trim();
  }
};

/**
 * تصدير بيانات إلى ملف Excel
 * @param data البيانات المراد تصديرها
 * @param fileName اسم الملف
 * @param sheetName اسم ورقة العمل
 * @param columnWidths عرض الأعمدة (اختياري)
 */
export const exportToExcel = (
  data: Record<string, unknown>[],
  fileName: string,
  sheetName: string,
  columnWidths?: { wch: number }[]
) => {
  try {
    if (data.length === 0) {
      toast.error('لا توجد بيانات للتصدير');
      return;
    }

    // إنشاء ورقة عمل Excel
    const worksheet = XLSX.utils.json_to_sheet(data);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);

    // تعيين عرض الأعمدة إذا تم توفيرها
    if (columnWidths) {
      worksheet['!cols'] = columnWidths;
    }

    // تصدير الملف
    try {
      // استخدام طريقة بديلة لحفظ الملف
      const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
      const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast.success('تم تصدير البيانات بنجاح');
    } catch (saveError) {
      console.error('Error saving Excel file:', saveError);
      toast.error('حدث خطأ أثناء حفظ ملف Excel');
    }
  } catch (error) {
    console.error('Error exporting to Excel:', error);
    toast.error('حدث خطأ أثناء تصدير البيانات');
  }
};

/**
 * تصدير بيانات إلى ملف PDF
 * هذه الدالة تستخدم طريقة بديلة لإنشاء PDF مع دعم كامل للغة العربية
 * تقوم بإنشاء جدول HTML ثم تحويله إلى PDF
 * @param options خيارات التصدير
 */
export const exportToPdf = (options: {
  title: string;
  fileName: string;
  schoolInfo?: {
    name: string;
    address?: string;
    phone?: string;
    email?: string;
  };
  tables: {
    title?: string;
    headers: string[];
    data: (string | number | null)[][];
    startY?: number;
    styles?: Partial<UserOptions['styles']>;
    headStyles?: Partial<UserOptions['headStyles']>;
    alternateRowStyles?: Partial<UserOptions['alternateRowStyles']>;
  }[];
  charts?: {
    title?: string;
    type: 'bar' | 'line' | 'pie' | 'doughnut';
    data: {
      labels: string[];
      datasets: {
        label: string;
        data: number[];
        backgroundColor?: string | string[];
        borderColor?: string | string[];
        borderWidth?: number;
      }[];
    };
    options?: {
      width?: number;
      height?: number;
    };
  }[];
  additionalContent?: {
    text: string;
    x: number;
    y: number;
    options?: {
      align?: 'left' | 'center' | 'right' | 'justify';
      baseline?: 'alphabetic' | 'ideographic' | 'bottom' | 'top' | 'middle' | 'hanging';
      angle?: number;
      rotationDirection?: 0 | 1;
      maxWidth?: number;
    };
  }[];
}) => {
  try {
    // إنشاء عنصر مؤقت لعرض البيانات
    const tempDiv = document.createElement('div');
    tempDiv.style.width = '210mm'; // عرض A4
    tempDiv.style.padding = '10mm';
    tempDiv.style.backgroundColor = 'white';
    tempDiv.style.direction = 'rtl';
    tempDiv.style.fontFamily = 'Arial, sans-serif';
    tempDiv.style.position = 'absolute';
    tempDiv.style.left = '-9999px';
    tempDiv.style.top = '0';

    // إضافة معلومات المدرسة إذا كانت متوفرة
    if (options.schoolInfo) {
      const schoolHeader = document.createElement('div');
      schoolHeader.style.textAlign = 'center';
      schoolHeader.style.marginBottom = '20px';
      schoolHeader.style.padding = '15px';
      schoolHeader.style.border = '2px solid #169b88';
      schoolHeader.style.borderRadius = '8px';
      schoolHeader.style.backgroundColor = '#f8fffd';

      // اسم المدرسة
      const schoolName = document.createElement('h1');
      schoolName.textContent = options.schoolInfo.name;
      schoolName.style.color = '#169b88';
      schoolName.style.margin = '0 0 10px 0';
      schoolName.style.fontSize = '24px';
      schoolName.style.fontWeight = 'bold';
      schoolHeader.appendChild(schoolName);

      // عنوان المدرسة
      if (options.schoolInfo.address) {
        const schoolAddress = document.createElement('p');
        schoolAddress.textContent = options.schoolInfo.address;
        schoolAddress.style.margin = '5px 0';
        schoolAddress.style.fontSize = '14px';
        schoolAddress.style.color = '#333';
        schoolHeader.appendChild(schoolAddress);
      }

      // هاتف المدرسة
      if (options.schoolInfo.phone) {
        const schoolPhone = document.createElement('p');
        schoolPhone.textContent = `الهاتف: ${options.schoolInfo.phone}`;
        schoolPhone.style.margin = '5px 0';
        schoolPhone.style.fontSize = '12px';
        schoolPhone.style.color = '#666';
        schoolHeader.appendChild(schoolPhone);
      }

      // بريد المدرسة الإلكتروني
      if (options.schoolInfo.email) {
        const schoolEmail = document.createElement('p');
        schoolEmail.textContent = `البريد الإلكتروني: ${options.schoolInfo.email}`;
        schoolEmail.style.margin = '5px 0';
        schoolEmail.style.fontSize = '12px';
        schoolEmail.style.color = '#666';
        schoolHeader.appendChild(schoolEmail);
      }

      tempDiv.appendChild(schoolHeader);
    }

    // إضافة العنوان
    const titleElement = document.createElement('h1');
    titleElement.textContent = options.title;
    titleElement.style.textAlign = 'center';
    titleElement.style.color = '#169b88';
    titleElement.style.margin = '20px 0 10px 0';
    titleElement.style.fontSize = '22px';
    titleElement.style.fontWeight = 'bold';
    titleElement.style.borderBottom = '2px solid #169b88';
    titleElement.style.paddingBottom = '10px';
    tempDiv.appendChild(titleElement);

    // إضافة التاريخ
    const today = new Date();
    const dateStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
    const dateElement = document.createElement('p');
    dateElement.textContent = `تاريخ التقرير: ${dateStr}`;
    dateElement.style.textAlign = 'center';
    dateElement.style.margin = '0 0 20px 0';
    dateElement.style.fontSize = '14px';
    dateElement.style.color = '#666';
    tempDiv.appendChild(dateElement);

    // إنشاء حاوية للجداول
    const tablesContainer = document.createElement('div');
    tablesContainer.style.width = '100%';
    tablesContainer.style.breakAfter = 'avoid'; // تجنب فواصل الصفحات داخل الجداول
    tempDiv.appendChild(tablesContainer);

    // إضافة الجداول
    for (let i = 0; i < options.tables.length; i++) {
      const table = options.tables[i];

      // إضافة عنوان الجدول إذا كان موجودًا
      if (table.title) {
        // الحصول على اللون الأساسي للموقع
        const primaryColor = getComputedStyle(document.documentElement).getPropertyValue('--primary-color').trim() || '#3b82f6';

        const tableTitle = document.createElement('h2');
        tableTitle.textContent = table.title;
        tableTitle.style.textAlign = 'center';
        tableTitle.style.color = primaryColor;
        tableTitle.style.margin = '25px 0 15px 0';
        tableTitle.style.fontSize = '20px';
        tableTitle.style.fontWeight = 'bold';
        tableTitle.style.fontFamily = 'Arial, sans-serif';
        tableTitle.style.backgroundColor = '#f8fffd';
        tableTitle.style.padding = '10px 15px';
        tableTitle.style.borderRadius = '8px';
        tableTitle.style.border = `2px solid ${primaryColor}`;
        tableTitle.style.display = 'block';
        tableTitle.style.width = '100%';
        tableTitle.style.boxSizing = 'border-box';
        tableTitle.style.breakBefore = 'avoid'; // تجنب فواصل الصفحات قبل العنوان
        tableTitle.style.breakAfter = 'avoid'; // تجنب فواصل الصفحات بعد العنوان
        tableTitle.style.breakInside = 'avoid'; // تجنب فواصل الصفحات داخل العنوان
        tableTitle.style.pageBreakBefore = 'avoid'; // دعم المتصفحات القديمة
        tableTitle.style.pageBreakAfter = 'avoid'; // دعم المتصفحات القديمة
        tableTitle.style.pageBreakInside = 'avoid'; // دعم المتصفحات القديمة
        tablesContainer.appendChild(tableTitle);
      }

      // الحصول على اللون الأساسي للموقع
      const primaryColor = getComputedStyle(document.documentElement).getPropertyValue('--primary-color').trim() || '#3b82f6';

      // إنشاء الجدول بطريقة مبسطة
      const tableHTML = `
        <table style="width: 100%; border-collapse: collapse; margin: 10px 0 25px 0; font-size: 12px; border: 1px solid #ddd;">
          <thead style="background-color: ${primaryColor}; color: white;">
            <tr>
              ${table.headers.map(header => `
                <th style="
                  background-color: ${primaryColor} !important;
                  color: white !important;
                  padding: 12px 10px;
                  text-align: center;
                  border: 1px solid #ddd;
                  font-weight: bold;
                  font-size: 13px;
                  font-family: Arial, sans-serif;
                ">${header}</th>
              `).join('')}
            </tr>
          </thead>
          <tbody>
            ${table.data.map((row, index) => `
              <tr style="background-color: ${index % 2 === 0 ? '#f9f9f9' : 'white'};">
                ${row.map(cell => `
                  <td style="
                    padding: 8px;
                    text-align: center;
                    border: 1px solid #ddd;
                  ">${cell !== null ? String(cell) : ''}</td>
                `).join('')}
              </tr>
            `).join('')}
          </tbody>
        </table>
      `;

      // إنشاء عنصر div وإضافة HTML إليه
      const tableContainer = document.createElement('div');
      tableContainer.innerHTML = tableHTML;

      // إضافة الجدول إلى الحاوية
      tablesContainer.appendChild(tableContainer.firstElementChild!);

      // إضافة مساحة بعد الجدول
      const spacer = document.createElement('div');
      spacer.style.height = '10px';
      tablesContainer.appendChild(spacer);

      // إضافة فاصل صفحة بعد كل جدول (إلا إذا كان الجدول الأخير)
      if (i < options.tables.length - 1 && table.data.length > 10) {
        const pageBreak = document.createElement('div');
        pageBreak.style.breakAfter = 'page';
        pageBreak.style.height = '1px';
        tablesContainer.appendChild(pageBreak);
      }
    }

    // إضافة الرسوم البيانية إذا كانت موجودة
    if (options.charts && options.charts.length > 0) {
      // إضافة فاصل صفحة قبل قسم الرسوم البيانية فقط إذا كانت هناك جداول
      if (options.tables && options.tables.length > 0) {
        const chartsPageBreak = document.createElement('div');
        chartsPageBreak.style.breakBefore = 'page';
        chartsPageBreak.style.height = '1px';
        tempDiv.appendChild(chartsPageBreak);
      }

      // إنشاء عنوان لقسم الرسوم البيانية
      const chartsTitle = document.createElement('h1');
      chartsTitle.textContent = 'الرسوم البيانية';
      chartsTitle.style.textAlign = 'center';
      chartsTitle.style.color = '#169b88';
      chartsTitle.style.margin = '20px 0';
      chartsTitle.style.fontSize = '24px';
      tempDiv.appendChild(chartsTitle);

      // إنشاء حاوية للرسوم البيانية
      const chartsContainer = document.createElement('div');
      chartsContainer.style.width = '100%';
      tempDiv.appendChild(chartsContainer);

      // إضافة الرسوم البيانية - كل رسم بياني في صفحة منفصلة
      for (let i = 0; i < options.charts.length; i++) {
        const chart = options.charts[i];

        // إضافة فاصل صفحة قبل كل رسم بياني (إلا الأول)
        if (i > 0) {
          const pageBreak = document.createElement('div');
          pageBreak.style.breakBefore = 'page';
          pageBreak.style.height = '1px';
          chartsContainer.appendChild(pageBreak);
        }

        // إنشاء حاوية للرسم البياني
        const chartWrapper = document.createElement('div');
        chartWrapper.style.width = '100%';
        chartWrapper.style.marginBottom = '30px';
        chartWrapper.style.breakInside = 'avoid'; // تجنب فواصل الصفحات داخل الرسم البياني
        chartsContainer.appendChild(chartWrapper);

        // إضافة عنوان الرسم البياني إذا كان موجودًا
        if (chart.title) {
          const chartTitle = document.createElement('h2');
          chartTitle.textContent = chart.title;
          chartTitle.style.textAlign = 'center';
          chartTitle.style.color = '#169b88';
          chartTitle.style.margin = '20px 0 10px 0';
          chartTitle.style.fontSize = '18px';
          chartTitle.style.breakBefore = 'avoid'; // تجنب فواصل الصفحات قبل العنوان
          chartTitle.style.breakAfter = 'avoid'; // تجنب فواصل الصفحات بعد العنوان
          chartWrapper.appendChild(chartTitle);
        }

        // إنشاء حاوية للرسم البياني
        const chartContainer = document.createElement('div');
        chartContainer.style.width = `${chart.options?.width || 400}px`; // تقليل العرض قليلاً
        chartContainer.style.height = `${chart.options?.height || 250}px`; // تقليل الارتفاع قليلاً
        chartContainer.style.margin = '0 auto 30px auto'; // زيادة الهامش السفلي
        chartContainer.style.position = 'relative';
        chartContainer.style.breakInside = 'avoid'; // منع تقسيم الرسم البياني (الخاصية الحديثة)
        chartContainer.style.breakBefore = 'auto'; // السماح بفاصل صفحة قبل الرسم البياني
        chartContainer.style.breakAfter = 'auto'; // السماح بفاصل صفحة بعد الرسم البياني
        chartWrapper.appendChild(chartContainer);

        // إنشاء صورة ثابتة للرسم البياني
        const chartImage = document.createElement('img');
        chartImage.style.width = '100%';
        chartImage.style.height = '100%';
        chartImage.style.objectFit = 'contain';
        chartImage.style.maxWidth = '100%';
        chartImage.style.display = 'block';
        chartImage.style.margin = '0 auto';
        chartImage.crossOrigin = 'anonymous'; // السماح بتحميل الصور من مصادر خارجية
        chartContainer.appendChild(chartImage);

        // تعيين ألوان افتراضية إذا لم يتم توفيرها
        const defaultColors = [
          '#169b88', '#2ecc71', '#3498db', '#9b59b6', '#f1c40f',
          '#e67e22', '#e74c3c', '#1abc9c', '#34495e', '#95a5a6'
        ];

        // إعداد بيانات الرسم البياني
        for (let j = 0; j < chart.data.datasets.length; j++) {
          const dataset = chart.data.datasets[j];

          // تعيين ألوان افتراضية إذا لم يتم توفيرها
          if (!dataset.backgroundColor) {
            if (chart.type === 'pie' || chart.type === 'doughnut') {
              // استخدام ألوان مختلفة لكل قطاع في الرسوم الدائرية
              dataset.backgroundColor = dataset.data.map((_, index) =>
                defaultColors[index % defaultColors.length]
              );
            } else {
              // استخدام لون واحد للرسوم الأخرى
              dataset.backgroundColor = defaultColors[j % defaultColors.length];
            }
          }

          if (!dataset.borderColor) {
            if (chart.type === 'line') {
              dataset.borderColor = defaultColors[j % defaultColors.length];
            }
          }

          if (dataset.borderWidth === undefined) {
            dataset.borderWidth = 1;
          }
        }

        // تحسين خيارات الرسم البياني للعرض في PDF
        const chartConfig = {
          type: chart.type,
          data: chart.data,
          options: {
            responsive: true,
            maintainAspectRatio: true,
            devicePixelRatio: 2, // زيادة دقة الرسم البياني
            plugins: {
              legend: {
                position: 'top',
                labels: {
                  font: {
                    family: 'Arial, sans-serif',
                    size: 12 // زيادة حجم الخط للوضوح
                  },
                  color: '#333333' // تحسين لون النص
                }
              },
              title: {
                display: false // إخفاء العنوان داخل الرسم البياني لأننا نضيفه كعنصر HTML
              },
              tooltip: {
                enabled: false // تعطيل التلميحات لأنها غير مفيدة في PDF
              }
            },
            layout: {
              padding: 10 // إضافة تباعد داخلي للرسم البياني
            },
            scales: chart.type !== 'pie' && chart.type !== 'doughnut' ? {
              x: {
                ticks: {
                  font: {
                    family: 'Arial, sans-serif',
                    size: 12 // زيادة حجم الخط للوضوح
                  },
                  color: '#333333' // تحسين لون النص
                },
                grid: {
                  color: '#dddddd' // تحسين لون الشبكة
                }
              },
              y: {
                ticks: {
                  font: {
                    family: 'Arial, sans-serif',
                    size: 12 // زيادة حجم الخط للوضوح
                  },
                  color: '#333333' // تحسين لون النص
                },
                grid: {
                  color: '#dddddd' // تحسين لون الشبكة
                }
              }
            } : undefined
          }
        };

        // تحويل تكوين الرسم البياني إلى سلسلة JSON وترميزه
        const chartConfigStr = encodeURIComponent(JSON.stringify(chartConfig));
        // استخدام خدمة QuickChart مع معلمات محسنة
        const chartUrl = `https://quickchart.io/chart?c=${chartConfigStr}&w=${chart.options?.width || 500}&h=${chart.options?.height || 300}&bkg=white&f=png&devicePixelRatio=2.0`;

        // تعيين مصدر الصورة
        chartImage.src = chartUrl;

        // إضافة معالج لتحميل الصورة
        chartImage.onload = () => {
          // إضافة فئة للإشارة إلى أن الصورة تم تحميلها
          chartImage.classList.add('loaded');
        };

        // إضافة نص بديل في حالة فشل تحميل الصورة
        chartImage.alt = chart.title || 'رسم بياني';
        chartImage.onerror = () => {
          console.error('فشل تحميل الرسم البياني:', chartUrl);
          const errorMessage = document.createElement('p');
          errorMessage.textContent = 'تعذر تحميل الرسم البياني';
          errorMessage.style.color = 'red';
          errorMessage.style.textAlign = 'center';
          errorMessage.style.fontWeight = 'bold';
          chartContainer.appendChild(errorMessage);

          // محاولة تحميل الصورة مرة أخرى بخيارات أبسط
          setTimeout(() => {
            const simpleChartConfig = {
              type: chart.type,
              data: chart.data,
              options: {
                plugins: { legend: { display: true } }
              }
            };
            const simpleChartUrl = `https://quickchart.io/chart?c=${encodeURIComponent(JSON.stringify(simpleChartConfig))}&w=400&h=250`;
            chartImage.src = simpleChartUrl;
          }, 500);
        };
      }
    }

    // إضافة محتوى إضافي إذا كان موجودًا
    if (options.additionalContent) {
      for (const content of options.additionalContent) {
        const additionalElement = document.createElement('p');
        additionalElement.textContent = content.text;
        additionalElement.style.textAlign = content.options?.align || 'center';
        additionalElement.style.color = '#666';
        additionalElement.style.fontSize = '12px';
        additionalElement.style.margin = '10px 0';
        tempDiv.appendChild(additionalElement);
      }
    }

    // إضافة العنصر المؤقت إلى الصفحة
    document.body.appendChild(tempDiv);

    // استخدام وقت انتظار معقول للسماح بتحميل وإنشاء الرسوم البيانية
    const chartsCount = options.charts?.length || 0;
    const tablesCount = options.tables.length;
    // وقت انتظار معقول بناءً على عدد الرسوم البيانية والجداول
    const waitTime = Math.max(500, chartsCount * 500 + tablesCount * 100);

    // إظهار رسالة للمستخدم
    toast.info('جاري إنشاء التقرير، يرجى الانتظار...');

    // استخدام setTimeout للتأكد من أن جميع العناصر تم تحميلها
    setTimeout(() => {
      try {
        console.log('بدء عملية تحويل HTML إلى PDF...');
        console.log('عدد الجداول:', options.tables.length);
        console.log('حجم العنصر المؤقت:', tempDiv.offsetWidth, 'x', tempDiv.offsetHeight);
        // استخدام html2canvas لتحويل العنصر المؤقت إلى صورة
        html2canvas(tempDiv, {
          scale: 2, // إرجاع الدقة إلى قيمة مستقرة
          useCORS: true, // السماح بتحميل الموارد من مصادر خارجية
          logging: false, // إيقاف السجلات
          allowTaint: true, // السماح بتضمين الصور من مصادر خارجية
          imageTimeout: 5000, // وقت انتظار معقول
          backgroundColor: '#ffffff', // تعيين لون خلفية أبيض
          onclone: (clonedDoc) => {
            // تحسين عرض العناصر في النسخة المستنسخة
            const clonedElement = clonedDoc.body.firstChild as HTMLElement;
            if (clonedElement) {
              // التأكد من أن جميع الصور محملة
              const images = clonedElement.querySelectorAll('img');
              images.forEach(img => {
                if (!img.complete) {
                  img.src = img.src; // إعادة تحميل الصورة
                }
              });

              // التأكد من ظهور عناوين الجداول (h2)
              const tableHeaders = clonedElement.querySelectorAll('h2');
              tableHeaders.forEach(header => {
                header.style.display = 'block';
                header.style.visibility = 'visible';
                header.style.opacity = '1';
              });
            }
            return clonedDoc;
          }
        }).then(canvas => {
          try {
            console.log('تم إنشاء Canvas بنجاح:', canvas.width, 'x', canvas.height);

            // التحقق من أن Canvas ليس فارغاً
            if (canvas.width === 0 || canvas.height === 0) {
              throw new Error('Canvas فارغ - لا يوجد محتوى للتحويل');
            }

            // الحصول على عرض وارتفاع الصورة
            const imgWidth = 210; // عرض A4 بالملم
            const pageHeight = 297; // ارتفاع A4 بالملم
            const imgHeight = canvas.height * imgWidth / canvas.width;

            console.log('أبعاد الصورة المحسوبة:', imgWidth, 'x', imgHeight);

            // حساب عدد الصفحات المطلوبة (للمعلومات فقط)
            // const pageCount = Math.ceil(imgHeight / pageHeight);

            // إنشاء مستند PDF جديد
            const pdf = new jsPDF({
              orientation: 'portrait',
              unit: 'mm',
              format: 'a4',
              compress: true
            });

            // تعيين خصائص المستند
            pdf.setProperties({
              title: options.title,
              subject: 'تقرير',
              author: 'نظام إدارة مدرسة القرآن',
              keywords: 'تقرير, قرآن, مدرسة',
              creator: 'نظام إدارة مدرسة القرآن'
            });

            // تحويل الصورة إلى صورة قاعدة64
            const imgData = canvas.toDataURL('image/png');

            // إضافة هوامش للصفحة
            const margin = 10; // هامش 10 ملم
            const printWidth = imgWidth - (margin * 2);
            const printHeight = imgHeight * (printWidth / imgWidth);

            // حساب عدد الصفحات المطلوبة
            const contentHeight = pageHeight - (margin * 2);
            const totalPages = Math.ceil(printHeight / contentHeight);

            // إضافة كل صفحة
            for (let i = 0; i < totalPages; i++) {
              if (i > 0) {
                pdf.addPage();
              }

              // إضافة جزء من الصورة إلى الصفحة الحالية
              // استخدام طريقة أبسط لإضافة الصورة
              if (i === 0) {
                // للصفحة الأولى، نضيف الصورة كاملة
                pdf.addImage(imgData, 'PNG', margin, margin, printWidth, printHeight);
              } else {
                // للصفحات الأخرى، نحسب الموضع بناءً على الصفحة الحالية
                const position = -contentHeight * i + margin;
                pdf.addImage(imgData, 'PNG', margin, position, printWidth, printHeight);
              }
            }

            // إضافة ترقيم الصفحات
            for (let i = 1; i <= pdf.getNumberOfPages(); i++) {
              pdf.setPage(i);
              pdf.setFontSize(10);
              pdf.setTextColor(100, 100, 100);
              pdf.text(`${i} / ${pdf.getNumberOfPages()}`, pdf.internal.pageSize.getWidth() - 20, pdf.internal.pageSize.getHeight() - 10);
            }

            // حفظ الملف
            console.log('حفظ ملف PDF:', options.fileName);
            pdf.save(options.fileName);
            toast.success('تم تصدير التقرير بنجاح');
          } catch (pdfError) {
            console.error('Error creating PDF from canvas:', pdfError);

            // محاولة بديلة لحفظ الملف كصورة
            try {
              const imgData = canvas.toDataURL('image/png');
              const link = document.createElement('a');
              link.href = imgData;
              link.download = options.fileName.replace('.pdf', '.png');
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);
              toast.success('تم تصدير التقرير كصورة بنجاح');
            } catch (imgError) {
              console.error('Error saving image:', imgError);
              toast.error('حدث خطأ أثناء تصدير التقرير');
            }
          }
        }).catch(canvasError => {
          console.error('Error creating canvas:', canvasError);
          console.error('تفاصيل الخطأ:', canvasError.message);
          toast.error('حدث خطأ أثناء إنشاء التقرير: ' + canvasError.message);
        }).finally(() => {
          // إزالة العنصر المؤقت من الصفحة
          try {
            if (document.body.contains(tempDiv)) {
              document.body.removeChild(tempDiv);
            }
          } catch (removeError) {
            console.warn('تحذير: لم يتم إزالة العنصر المؤقت:', removeError);
          }
        });
      } catch (error) {
        console.error('Error in PDF export process:', error);
        toast.error('حدث خطأ أثناء إنشاء ملف PDF');
        document.body.removeChild(tempDiv);
      }
    }, waitTime);
  } catch (error) {
    console.error('Error creating PDF:', error);
    toast.error('حدث خطأ أثناء إنشاء ملف PDF');
  }
};

/**
 * تصدير تقرير تحليل النتائج
 * @param analysisData بيانات التحليل
 * @param section القسم المراد تصديره
 * @param format صيغة التصدير (pdf أو excel)
 * @param fileName اسم الملف
 */
// تعريف واجهة بيانات التحليل
interface AnalysisData {
  summary?: {
    totalStudents: number;
    passedStudents: number;
    failedStudents: number;
    excellentStudents: number;
    passRate: number;
    averageGrade: number;
    highestGrade: number;
    lowestGrade: number;
  };
  gradeDistribution?: {
    excellent: number;
    veryGood: number;
    good: number;
    fair: number;
    poor: number;
    veryPoor: number;
  };
  classeAnalysis?: Array<{
    name: string;
    totalStudents: number;
    passedStudents: number;
    failedStudents: number;
    excellentStudents: number;
    averageGrade: number;
  }>;
  teacherAnalysis?: Array<{
    name: string;
    totalStudents: number;
    passedStudents: number;
    failedStudents: number;
    excellentStudents: number;
    averageGrade: number;
  }>;
  questionTypeAnalysis?: Array<{
    type: string;
    totalAnswers: number;
    correctAnswers: number;
    incorrectAnswers: number;
    averagePoints: number;
  }>;
  difficultyAnalysis?: Array<{
    level: string;
    totalAnswers: number;
    correctAnswers: number;
    incorrectAnswers: number;
    averagePoints: number;
  }>;
  genderAnalysis?: Record<string, {
    totalStudents: number;
    passedStudents: number;
    failedStudents: number;
    excellentStudents: number;
    averageGrade: number;
  }>;
}

export const exportAnalysisReport = (
  analysisData: AnalysisData,
  section: string,
  format: 'pdf' | 'excel',
  fileName: string = 'analysis-report'
) => {
  if (!analysisData) {
    toast.error('لا توجد بيانات للتصدير');
    return;
  }

  try {
    // تحديد البيانات المراد تصديرها بناءً على القسم
    let title = '';
    let headers: string[] = [];
    let data: (string | number | null)[][] = [];
    let summary: Record<string, string | number | null> = {};
    let chartIds: { id: string; title: string }[] = [];

    switch (section) {
      case 'summary':
        if (!analysisData.summary || !analysisData.gradeDistribution) {
          toast.error('بيانات الملخص غير متوفرة');
          return;
        }

        title = 'ملخص نتائج الامتحانات';
        headers = ['المؤشر', 'القيمة'];
        data = [
          ['إجمالي الطلاب', analysisData.summary.totalStudents],
          ['الطلاب الناجحون', analysisData.summary.passedStudents],
          ['الطلاب الراسبون', analysisData.summary.failedStudents],
          ['الطلاب المتميزون', analysisData.summary.excellentStudents],
          ['نسبة النجاح', `${analysisData.summary.passRate.toFixed(1)}%`],
          ['متوسط الدرجات', analysisData.summary.averageGrade.toFixed(1)],
          ['أعلى درجة', analysisData.summary.highestGrade.toFixed(1)],
          ['أدنى درجة', analysisData.summary.lowestGrade.toFixed(1)]
        ];
        summary = {
          'توزيع الدرجات': `ممتاز: ${analysisData.gradeDistribution.excellent}, جيد جداً: ${analysisData.gradeDistribution.veryGood}, جيد: ${analysisData.gradeDistribution.good}, مقبول: ${analysisData.gradeDistribution.fair}, ضعيف: ${analysisData.gradeDistribution.poor}, ضعيف جداً: ${analysisData.gradeDistribution.veryPoor}`
        };
        chartIds = [
          { id: 'pie-chart', title: 'توزيع النتائج' },
          { id: 'bar-chart', title: 'حالة الطلاب' }
        ];
        break;

      case 'classes':
        if (!analysisData.classeAnalysis || analysisData.classeAnalysis.length === 0) {
          toast.error('بيانات الفصول غير متوفرة');
          return;
        }

        title = 'تحليل أداء الفصول';
        headers = ['الفصل', 'عدد الطلاب', 'متوسط الدرجات', 'نسبة النجاح', 'الطلاب المتميزون', 'الطلاب الراسبون'];
        data = analysisData.classeAnalysis.map((classe) => [
          classe.name,
          classe.totalStudents,
          classe.averageGrade.toFixed(1),
          classe.totalStudents > 0 ? `${((classe.passedStudents / classe.totalStudents) * 100).toFixed(1)}%` : '0%',
          classe.excellentStudents,
          classe.failedStudents
        ]);
        chartIds = [
          { id: 'bar-chart', title: 'متوسط الدرجات حسب الفصل' }
        ];
        break;

      case 'teachers':
        if (!analysisData.teacherAnalysis || analysisData.teacherAnalysis.length === 0) {
          toast.error('بيانات المعلمين غير متوفرة');
          return;
        }

        title = 'تحليل أداء المعلمين';
        headers = ['المعلم', 'عدد الطلاب', 'متوسط الدرجات', 'نسبة النجاح', 'الطلاب المتميزون', 'الطلاب الراسبون'];
        data = analysisData.teacherAnalysis.map((teacher) => [
          teacher.name,
          teacher.totalStudents,
          teacher.averageGrade.toFixed(1),
          teacher.totalStudents > 0 ? `${((teacher.passedStudents / teacher.totalStudents) * 100).toFixed(1)}%` : '0%',
          teacher.excellentStudents,
          teacher.failedStudents
        ]);
        chartIds = [
          { id: 'bar-chart', title: 'متوسط الدرجات حسب المعلم' }
        ];
        break;

      case 'questions':
        if (!analysisData.questionTypeAnalysis || analysisData.questionTypeAnalysis.length === 0) {
          toast.error('بيانات أنواع الأسئلة غير متوفرة');
          return;
        }

        title = 'تحليل أداء الطلاب حسب نوع السؤال';
        headers = ['نوع السؤال', 'عدد الإجابات', 'الإجابات الصحيحة', 'الإجابات الخاطئة', 'نسبة الإجابات الصحيحة', 'متوسط النقاط'];
        data = analysisData.questionTypeAnalysis.map((type) => {
          const questionTypeLabels: Record<string, string> = {
            MULTIPLE_CHOICE: 'اختيار من متعدد',
            TRUE_FALSE: 'صح أو خطأ',
            SHORT_ANSWER: 'إجابة قصيرة',
            ESSAY: 'مقال',
            MATCHING: 'مطابقة',
            FILL_BLANK: 'ملء الفراغات',
            ORDERING: 'ترتيب'
          };

          return [
            questionTypeLabels[type.type] || type.type,
            type.totalAnswers,
            type.correctAnswers,
            type.incorrectAnswers,
            (type.correctAnswers + type.incorrectAnswers) > 0
              ? `${((type.correctAnswers / (type.correctAnswers + type.incorrectAnswers)) * 100).toFixed(1)}%`
              : '0%',
            type.averagePoints.toFixed(2)
          ];
        });
        chartIds = [
          { id: 'bar-chart', title: 'نسبة الإجابات الصحيحة حسب نوع السؤال' }
        ];
        break;

      case 'difficulty':
        if (!analysisData.difficultyAnalysis || analysisData.difficultyAnalysis.length === 0) {
          toast.error('بيانات مستويات الصعوبة غير متوفرة');
          return;
        }

        title = 'تحليل أداء الطلاب حسب مستوى الصعوبة';
        headers = ['مستوى الصعوبة', 'عدد الإجابات', 'الإجابات الصحيحة', 'الإجابات الخاطئة', 'نسبة الإجابات الصحيحة', 'متوسط النقاط'];
        data = analysisData.difficultyAnalysis.map((level) => {
          const difficultyLevelLabels: Record<string, string> = {
            EASY: 'سهل',
            MEDIUM: 'متوسط',
            HARD: 'صعب',
            VERY_HARD: 'صعب جداً'
          };

          return [
            difficultyLevelLabels[level.level] || level.level,
            level.totalAnswers,
            level.correctAnswers,
            level.incorrectAnswers,
            (level.correctAnswers + level.incorrectAnswers) > 0
              ? `${((level.correctAnswers / (level.correctAnswers + level.incorrectAnswers)) * 100).toFixed(1)}%`
              : '0%',
            level.averagePoints.toFixed(2)
          ];
        });
        chartIds = [
          { id: 'line-chart', title: 'العلاقة بين مستوى الصعوبة ونسبة الإجابات الصحيحة' }
        ];
        break;

      case 'gender':
        if (!analysisData.genderAnalysis || Object.keys(analysisData.genderAnalysis).length === 0) {
          toast.error('بيانات التحليل حسب الجنس غير متوفرة');
          return;
        }

        title = 'تحليل النتائج حسب الجنس';
        headers = ['الجنس', 'عدد الطلاب', 'متوسط الدرجات', 'نسبة النجاح', 'الطلاب المتميزون', 'الطلاب الراسبون'];
        data = Object.entries(analysisData.genderAnalysis).map(([gender, data]) => {
          const genderLabel = gender === 'MALE' ? 'ذكور' : 'إناث';

          return [
            genderLabel,
            data.totalStudents,
            data.averageGrade.toFixed(1),
            data.totalStudents > 0 ? `${((data.passedStudents / data.totalStudents) * 100).toFixed(1)}%` : '0%',
            data.excellentStudents,
            data.failedStudents
          ];
        });
        chartIds = [
          { id: 'bar-chart', title: 'مقارنة متوسط الدرجات حسب الجنس' }
        ];
        break;

      default:
        toast.error('قسم غير معروف');
        return;
    }

    // تصدير البيانات حسب الصيغة المطلوبة
    if (format === 'excel') {
      // تحويل البيانات إلى تنسيق مناسب لـ Excel
      const excelData = [
        [title], // عنوان التقرير
        [], // سطر فارغ
        headers, // رؤوس الأعمدة
        ...data // البيانات
      ];

      // إنشاء ورقة عمل Excel
      const worksheet = XLSX.utils.aoa_to_sheet(excelData);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'البيانات');

      // إضافة ورقة عمل للملخص إذا كان موجودًا
      if (Object.keys(summary).length > 0) {
        const summaryData = [
          ['ملخص البيانات'],
          [],
          ...Object.entries(summary).map(([key, value]) => [key, value])
        ];
        const summaryWorksheet = XLSX.utils.aoa_to_sheet(summaryData);
        XLSX.utils.book_append_sheet(workbook, summaryWorksheet, 'الملخص');
      }

      // تصدير الملف
      try {
        const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
        const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `${fileName}.xlsx`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        toast.success('تم تصدير البيانات بنجاح');
      } catch (saveError) {
        console.error('Error saving Excel file:', saveError);
        toast.error('حدث خطأ أثناء حفظ ملف Excel');
      }
    } else if (format === 'pdf') {
      // تصدير إلى PDF
      exportToPdf({
        title,
        fileName: `${fileName}.pdf`,
        tables: [
          {
            headers,
            data,
            styles: {
              fontSize: 10,
              cellPadding: 3
            },
            headStyles: {
              fillColor: [22, 155, 136],
              textColor: [255, 255, 255],
              fontStyle: 'bold'
            }
          }
        ],
        charts: chartIds.map(chart => ({
          title: chart.title,
          type: 'bar',
          data: {
            labels: [],
            datasets: []
          },
          options: {
            width: 500,
            height: 300
          }
        })),
        additionalContent: Object.keys(summary).length > 0 ? [
          {
            text: 'ملخص البيانات: ' + Object.entries(summary).map(([key, value]) => `${key}: ${value}`).join(' | '),
            x: 10,
            y: 10,
            options: {
              align: 'center'
            }
          }
        ] : undefined
      });
    }
  } catch (error) {
    console.error('Error exporting analysis report:', error);
    toast.error('حدث خطأ أثناء تصدير التقرير');
  }
};

/**
 * تصدير بيانات إلى ملف CSV
 * @param data البيانات المراد تصديرها
 * @param fileName اسم الملف
 * @param headers رؤوس الأعمدة (اختياري)
 */
export const exportToCsv = (
  data: Record<string, unknown>[] | (string | number | null)[][],
  fileName: string,
  headers?: string[]
) => {
  try {
    if (Array.isArray(data) && data.length === 0) {
      toast.error('لا توجد بيانات للتصدير');
      return;
    }

    let csvContent = '';

    // إضافة رؤوس الأعمدة إذا تم توفيرها
    if (headers && headers.length > 0) {
      csvContent += headers.map(header => `"${header}"`).join(',') + '\r\n';
    } else if (!Array.isArray(data[0]) && typeof data[0] === 'object') {
      // استخراج رؤوس الأعمدة من المفاتيح إذا كانت البيانات عبارة عن مصفوفة من الكائنات
      const firstItem = data[0] as Record<string, unknown>;
      csvContent += Object.keys(firstItem).map(key => `"${key}"`).join(',') + '\r\n';
    }

    // إضافة البيانات
    if (Array.isArray(data[0])) {
      // إذا كانت البيانات عبارة عن مصفوفة من المصفوفات
      const arrayData = data as (string | number | null)[][];
      csvContent += arrayData.map(row =>
        row.map(cell =>
          cell === null ? '""' : typeof cell === 'string' ? `"${cell.replace(/"/g, '""')}"` : `"${cell}"`
        ).join(',')
      ).join('\r\n');
    } else {
      // إذا كانت البيانات عبارة عن مصفوفة من الكائنات
      const objectData = data as Record<string, unknown>[];
      csvContent += objectData.map(row =>
        Object.values(row).map(cell =>
          cell === null ? '""' : typeof cell === 'string' ? `"${(cell as string).replace(/"/g, '""')}"` : `"${cell}"`
        ).join(',')
      ).join('\r\n');
    }

    // إنشاء Blob وتنزيل الملف
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName.endsWith('.csv') ? fileName : `${fileName}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    toast.success('تم تصدير البيانات بنجاح');
  } catch (error) {
    console.error('Error exporting to CSV:', error);
    toast.error('حدث خطأ أثناء تصدير البيانات');
  }
};

/**
 * تصدير بيانات إلى صفحة HTML
 * @param options خيارات التصدير
 */
export const exportToHtml = (options: {
  title: string;
  fileName: string;
  tables: {
    title?: string;
    headers: string[];
    data: (string | number | null)[][];
    styles?: Record<string, string>;
    headerStyles?: Record<string, string>;
  }[];
  charts?: {
    title?: string;
    type: 'bar' | 'line' | 'pie' | 'doughnut';
    data: {
      labels: string[];
      datasets: {
        label: string;
        data: number[];
        backgroundColor?: string | string[];
        borderColor?: string | string[];
        borderWidth?: number;
      }[];
    };
    options?: {
      width?: number;
      height?: number;
    };
  }[];
  additionalContent?: string;
  recommendations?: Recommendation[];
  styles?: Record<string, string>;
}) => {
  try {
    // إنشاء محتوى HTML
    let htmlContent = `
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${options.title}</title>
  <style>
    body {
      font-family: 'Arial', 'Tajawal', sans-serif;
      line-height: 1.6;
      color: #333;
      background-color: #f9f9f9;
      margin: 0;
      padding: 20px;
      direction: rtl;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      background-color: #fff;
      padding: 20px;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
      border-radius: 5px;
    }
    h1, h2, h3 {
      color: #169b88;
      text-align: center;
      margin-top: 20px;
    }
    h1 {
      font-size: 24px;
      margin-bottom: 10px;
    }
    h2 {
      font-size: 20px;
      margin-bottom: 15px;
    }
    h3 {
      font-size: 18px;
      margin-bottom: 10px;
    }
    .date {
      text-align: center;
      color: #666;
      margin-bottom: 20px;
      font-size: 14px;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
      font-size: 14px;
    }
    th {
      background-color: #169b88;
      color: white;
      padding: 10px;
      text-align: center;
      font-weight: bold;
      border: 1px solid #ddd;
    }
    td {
      padding: 8px;
      text-align: center;
      border: 1px solid #ddd;
    }
    tr:nth-child(even) {
      background-color: #f9f9f9;
    }
    .chart-container {
      width: 100%;
      max-width: 600px;
      margin: 20px auto;
      text-align: center;
    }
    .chart-placeholder {
      width: 100%;
      height: 300px;
      background-color: #f0f0f0;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px dashed #ccc;
      border-radius: 5px;
      color: #666;
      font-style: italic;
    }
    .additional-content {
      margin: 20px 0;
      padding: 15px;
      background-color: #f5f5f5;
      border-radius: 5px;
      border-right: 3px solid #169b88;
    }
    .recommendations {
      margin: 20px 0;
    }
    .recommendation {
      margin-bottom: 15px;
      padding: 15px;
      border-radius: 5px;
      background-color: #f8f8f8;
    }
    .recommendation h4 {
      margin-top: 0;
      color: #333;
    }
    .priority-high {
      border-right: 4px solid #e74c3c;
    }
    .priority-medium {
      border-right: 4px solid #f39c12;
    }
    .priority-low {
      border-right: 4px solid #2ecc71;
    }
    .footer {
      text-align: center;
      margin-top: 30px;
      padding-top: 10px;
      border-top: 1px solid #eee;
      color: #777;
      font-size: 12px;
    }
    @media print {
      body {
        background-color: white;
        padding: 0;
      }
      .container {
        box-shadow: none;
        padding: 0;
      }
      .no-print {
        display: none;
      }
    }
    ${options.styles ? Object.entries(options.styles).map(([selector, rules]) => `${selector} { ${rules} }`).join('\n') : ''}
  </style>
</head>
<body>
  <div class="container">
    <h1>${options.title}</h1>
    <p class="date">تاريخ التقرير: ${new Date().toLocaleDateString('fr-FR')}</p>
`;

    // إضافة الجداول
    if (options.tables && options.tables.length > 0) {
      options.tables.forEach(table => {
        if (table.title) {
          htmlContent += `    <h2>${table.title}</h2>\n`;
        }

        htmlContent += '    <div style="overflow-x: auto;">\n';
        htmlContent += '      <table>\n';
        htmlContent += '        <thead>\n';
        htmlContent += '          <tr>\n';

        // إضافة رؤوس الأعمدة
        table.headers.forEach(header => {
          const headerStyle = table.headerStyles ? ` style="${Object.entries(table.headerStyles).map(([prop, value]) => `${prop}: ${value}`).join('; ')}"` : '';
          htmlContent += `            <th${headerStyle}>${header}</th>\n`;
        });

        htmlContent += '          </tr>\n';
        htmlContent += '        </thead>\n';
        htmlContent += '        <tbody>\n';

        // إضافة بيانات الجدول
        table.data.forEach(row => {
          const rowStyle = table.styles ? ` style="${Object.entries(table.styles).map(([prop, value]) => `${prop}: ${value}`).join('; ')}"` : '';
          htmlContent += `          <tr${rowStyle}>\n`;

          row.forEach(cell => {
            htmlContent += `            <td>${cell !== null ? cell : ''}</td>\n`;
          });

          htmlContent += '          </tr>\n';
        });

        htmlContent += '        </tbody>\n';
        htmlContent += '      </table>\n';
        htmlContent += '    </div>\n';
      });
    }

    // إضافة الرسوم البيانية
    if (options.charts && options.charts.length > 0) {
      htmlContent += '    <h2>الرسوم البيانية</h2>\n';

      options.charts.forEach(chart => {
        htmlContent += '    <div class="chart-container">\n';

        if (chart.title) {
          htmlContent += `      <h3>${chart.title}</h3>\n`;
        }

        htmlContent += '      <div class="chart-placeholder">\n';
        htmlContent += '        الرسم البياني غير متاح في النسخة المصدرة. يرجى الرجوع إلى التطبيق لعرض الرسوم البيانية.\n';
        htmlContent += '      </div>\n';
        htmlContent += '    </div>\n';
      });
    }

    // إضافة التوصيات إذا كانت موجودة
    if (options.recommendations && options.recommendations.length > 0) {
      htmlContent += formatRecommendations(options.recommendations, 'html');
    }

    // إضافة محتوى إضافي إذا كان موجودًا
    if (options.additionalContent) {
      htmlContent += `    <div class="additional-content">\n`;
      htmlContent += `      ${options.additionalContent}\n`;
      htmlContent += `    </div>\n`;
    }

    // إضافة تذييل الصفحة
    htmlContent += `    <div class="footer">
      تم إنشاء هذا التقرير بواسطة نظام إدارة مدرسة القرآن - ${new Date().getFullYear()}
    </div>
    <div class="no-print" style="text-align: center; margin-top: 20px;">
      <button onclick="window.print()" style="padding: 8px 16px; background-color: #169b88; color: white; border: none; border-radius: 4px; cursor: pointer;">
        طباعة التقرير
      </button>
    </div>
  </div>
</body>
</html>
`;

    // إنشاء Blob وتنزيل الملف
    const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = options.fileName.endsWith('.html') ? options.fileName : `${options.fileName}.html`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    toast.success('تم تصدير التقرير بنجاح');
  } catch (error) {
    console.error('Error exporting to HTML:', error);
    toast.error('حدث خطأ أثناء تصدير التقرير');
  }
};

/**
 * تصدير تقرير مع توصيات آلية
 * @param data بيانات التقرير
 * @param analysisData بيانات التحليل لإنشاء التوصيات
 * @param format صيغة التصدير
 * @param fileName اسم الملف
 * @param options خيارات إضافية
 */
export const exportReportWithRecommendations = (
  data: {
    title: string;
    tables: {
      title?: string;
      headers: string[];
      data: (string | number | null)[][];
    }[];
    charts?: {
      title?: string;
      type: 'bar' | 'line' | 'pie' | 'doughnut';
      data: {
        labels: string[];
        datasets: {
          label: string;
          data: number[];
          backgroundColor?: string | string[];
          borderColor?: string | string[];
        }[];
      };
      options?: {
        width?: number;
        height?: number;
      };
    }[];
    additionalContent?: string;
  },
  analysisData: Record<string, unknown>,
  format: 'pdf' | 'excel' | 'csv' | 'html',
  fileName: string = 'report',
  options?: {
    recommendationType?: 'student' | 'class' | 'exam';
    includeRecommendations?: boolean;
  }
) => {
  try {
    // إنشاء التوصيات بناءً على نوع البيانات
    let recommendations: Recommendation[] = [];

    if (options?.includeRecommendations !== false) {
      if (options?.recommendationType === 'student') {
        recommendations = generateStudentRecommendations(analysisData as unknown as import('@/utils/recommendations-utils').StudentAnalysisData);
      } else if (options?.recommendationType === 'class') {
        recommendations = generateClassRecommendations(analysisData as unknown as import('@/utils/recommendations-utils').ClassAnalysisData);
      } else if (options?.recommendationType === 'exam') {
        // تحويل البيانات إلى الشكل المطلوب لـ ExamAnalysisData
        const examAnalysisData = convertToExamAnalysisData(analysisData);
        recommendations = analyzeExamAndGenerateRecommendations(examAnalysisData);
      }

      // دالة لتحويل البيانات إلى ExamAnalysisData
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      function convertToExamAnalysisData(data: Record<string, any>): import('@/utils/recommendations-utils').ExamAnalysisData {
        // إنشاء كائن يحتوي على الحقول المطلوبة
        return {
          summary: {
            totalStudents: data.summary?.totalStudents || 0,
            passedStudents: data.summary?.passedStudents || 0,
            failedStudents: data.summary?.failedStudents || 0,
            excellentStudents: data.summary?.excellentStudents || 0,
            pendingStudents: data.summary?.pendingStudents || 0,
            passRate: data.summary?.passRate || 0,
            averageGrade: data.summary?.averageGrade || 0,
            highestGrade: data.summary?.highestGrade || 0,
            lowestGrade: data.summary?.lowestGrade || 0
          },
          gradeDistribution: data.gradeDistribution,
          classeAnalysis: data.classeAnalysis,
          teacherAnalysis: data.teacherAnalysis,
          difficultyAnalysis: data.difficultyAnalysis,
          questionTypeAnalysis: data.questionTypeAnalysis,
          genderAnalysis: data.genderAnalysis
        };
      }
    }

    // تصدير البيانات حسب الصيغة المطلوبة
    switch (format) {
      case 'pdf':
        exportToPdf({
          title: data.title,
          fileName: fileName.endsWith('.pdf') ? fileName : `${fileName}.pdf`,
          tables: data.tables,
          charts: data.charts,
          additionalContent: recommendations.length > 0 ? [
            {
              text: formatRecommendations(recommendations, 'text'),
              x: 10,
              y: 10,
              options: {
                align: 'right'
              }
            }
          ] : undefined
        });
        break;

      case 'excel':
        // تحويل البيانات إلى تنسيق مناسب لـ Excel
        const excelData: Record<string, unknown>[] = [];

        // إضافة بيانات من كل جدول
        data.tables.forEach(table => {
          // إضافة عنوان الجدول كصف منفصل إذا كان موجودًا
          if (table.title) {
            const titleRow: Record<string, unknown> = {};
            titleRow[table.headers[0]] = table.title;
            excelData.push(titleRow);
          }

          // إضافة بيانات الجدول
          table.data.forEach(row => {
            const rowData: Record<string, unknown> = {};
            table.headers.forEach((header, index) => {
              rowData[header] = row[index];
            });
            excelData.push(rowData);
          });

          // إضافة صف فارغ بين الجداول
          const emptyRow: Record<string, unknown> = {};
          excelData.push(emptyRow);
        });

        // إضافة التوصيات إذا كانت موجودة
        if (recommendations.length > 0) {
          const recommendationsTitle: Record<string, unknown> = {};
          recommendationsTitle['التوصيات'] = 'التوصيات والإجراءات المقترحة';
          excelData.push(recommendationsTitle);

          recommendations.forEach(rec => {
            const recRow: Record<string, unknown> = {};
            recRow['العنوان'] = rec.title;
            recRow['الوصف'] = rec.description;
            recRow['الأولوية'] = rec.priority === 'high' ? 'عالية' : rec.priority === 'medium' ? 'متوسطة' : 'منخفضة';
            excelData.push(recRow);
          });
        }

        exportToExcel(
          excelData,
          fileName.endsWith('.xlsx') ? fileName : `${fileName}.xlsx`,
          'التقرير'
        );
        break;

      case 'csv':
        // تحويل البيانات إلى تنسيق مناسب لـ CSV
        const csvData: (string | number | null)[][] = [];
        const csvHeaders: string[] = [];

        // جمع جميع رؤوس الأعمدة الفريدة من جميع الجداول
        data.tables.forEach(table => {
          table.headers.forEach(header => {
            if (!csvHeaders.includes(header)) {
              csvHeaders.push(header);
            }
          });
        });

        // إضافة بيانات من كل جدول
        data.tables.forEach(table => {
          // إضافة عنوان الجدول كصف منفصل إذا كان موجودًا
          if (table.title) {
            const titleRow: (string | number | null)[] = new Array(csvHeaders.length).fill(null);
            titleRow[0] = table.title;
            csvData.push(titleRow);
          }

          // إضافة بيانات الجدول
          table.data.forEach(row => {
            const csvRow: (string | number | null)[] = new Array(csvHeaders.length).fill(null);

            table.headers.forEach((header, index) => {
              const headerIndex = csvHeaders.indexOf(header);
              if (headerIndex !== -1) {
                csvRow[headerIndex] = row[index];
              }
            });

            csvData.push(csvRow);
          });

          // إضافة صف فارغ بين الجداول
          csvData.push(new Array(csvHeaders.length).fill(null));
        });

        // إضافة التوصيات إذا كانت موجودة
        if (recommendations.length > 0) {
          const recommendationsTitle: (string | number | null)[] = new Array(csvHeaders.length).fill(null);
          recommendationsTitle[0] = 'التوصيات والإجراءات المقترحة';
          csvData.push(recommendationsTitle);

          recommendations.forEach(rec => {
            const recRow: (string | number | null)[] = new Array(csvHeaders.length).fill(null);
            recRow[0] = rec.title;
            recRow[1] = rec.description;
            recRow[2] = rec.priority === 'high' ? 'عالية' : rec.priority === 'medium' ? 'متوسطة' : 'منخفضة';
            csvData.push(recRow);
          });
        }

        exportToCsv(
          csvData,
          fileName.endsWith('.csv') ? fileName : `${fileName}.csv`,
          csvHeaders
        );
        break;

      case 'html':
        exportToHtml({
          title: data.title,
          fileName: fileName.endsWith('.html') ? fileName : `${fileName}.html`,
          tables: data.tables,
          charts: data.charts,
          additionalContent: data.additionalContent,
          recommendations: recommendations
        });
        break;

      default:
        toast.error('صيغة تصدير غير مدعومة');
        break;
    }
  } catch (error) {
    console.error('Error exporting report with recommendations:', error);
    toast.error('حدث خطأ أثناء تصدير التقرير');
  }
};

/**
 * تنزيل قالب Excel
 * @param templateData بيانات القالب
 * @param fileName اسم الملف
 * @param sheetName اسم ورقة العمل
 * @param columnWidths عرض الأعمدة (اختياري)
 */
export const downloadExcelTemplate = (
  templateData: Record<string, unknown>[],
  fileName: string,
  sheetName: string,
  columnWidths?: { wch: number }[]
) => {
  try {
    // إنشاء ورقة عمل Excel
    const worksheet = XLSX.utils.json_to_sheet(templateData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);

    // تعيين عرض الأعمدة إذا تم توفيرها
    if (columnWidths) {
      worksheet['!cols'] = columnWidths;
    }

    // تصدير الملف
    try {
      // استخدام طريقة بديلة لحفظ الملف
      const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
      const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast.success('تم تنزيل القالب بنجاح');
    } catch (saveError) {
      console.error('Error saving template file:', saveError);
      toast.error('حدث خطأ أثناء حفظ ملف القالب');
    }
  } catch (error) {
    console.error('Error creating template:', error);
    toast.error('حدث خطأ أثناء إنشاء القالب');
  }
};

/**
 * تصدير بيانات إلى ملف Word
 * @param options خيارات التصدير
 */
export const exportToWord = async (options: {
  title: string;
  fileName: string;
  tables?: Array<{
    title?: string;
    headers: string[];
    data: (string | number)[][];
  }>;
  content?: string;
  schoolInfo?: {
    name: string;
    address?: string;
  };
}) => {
  try {
    // استيراد مكتبة docx
    const { Document, Packer, Paragraph, TextRun, HeadingLevel, Table, TableRow, TableCell, WidthType, AlignmentType } = await import('docx');

    const children: any[] = [];

    // إضافة معلومات المدرسة/المؤسسة
    if (options.schoolInfo) {
      children.push(
        new Paragraph({
          children: [
            new TextRun({
              text: options.schoolInfo.name,
              bold: true,
              size: 32,
            }),
          ],
          heading: HeadingLevel.TITLE,
          alignment: AlignmentType.CENTER,
        })
      );

      if (options.schoolInfo.address) {
        children.push(
          new Paragraph({
            children: [
              new TextRun({
                text: options.schoolInfo.address,
                bold: true,
                size: 24,
              }),
            ],
            alignment: AlignmentType.CENTER,
          })
        );
      }
    }

    // إضافة العنوان الرئيسي
    children.push(
      new Paragraph({
        children: [
          new TextRun({
            text: options.title,
            bold: true,
            size: 28,
            color: "2563eb",
          }),
        ],
        heading: HeadingLevel.HEADING_1,
        alignment: AlignmentType.CENTER,
      })
    );

    // إضافة المحتوى النصي إذا كان موجوداً
    if (options.content) {
      // تنظيف المحتوى من علامات HTML
      const cleanedContent = cleanHtmlText(options.content);
      const contentLines = cleanedContent.split('\n');
      contentLines.forEach(line => {
        if (line.trim()) {
          children.push(
            new Paragraph({
              children: [
                new TextRun({
                  text: line.trim(),
                  size: 20,
                }),
              ],
            })
          );
        } else {
          children.push(new Paragraph({})); // سطر فارغ
        }
      });
    }

    // إضافة الجداول إذا كانت موجودة
    if (options.tables && options.tables.length > 0) {
      options.tables.forEach(table => {
        // عنوان الجدول
        if (table.title) {
          children.push(
            new Paragraph({
              children: [
                new TextRun({
                  text: table.title,
                  bold: true,
                  size: 24,
                  color: "059669",
                }),
              ],
              heading: HeadingLevel.HEADING_2,
            })
          );
        }

        // إنشاء الجدول
        const tableRows = [
          // رأس الجدول
          new TableRow({
            children: table.headers.map(header =>
              new TableCell({
                children: [
                  new Paragraph({
                    children: [
                      new TextRun({
                        text: header,
                        bold: true,
                        color: "ffffff",
                      }),
                    ],
                    alignment: AlignmentType.CENTER,
                  }),
                ],
                shading: {
                  fill: "059669",
                },
              })
            ),
          }),
          // بيانات الجدول
          ...table.data.map(row =>
            new TableRow({
              children: row.map(cell =>
                new TableCell({
                  children: [
                    new Paragraph({
                      children: [
                        new TextRun({
                          text: String(cell),
                        }),
                      ],
                      alignment: AlignmentType.CENTER,
                    }),
                  ],
                })
              ),
            })
          ),
        ];

        children.push(
          new Table({
            width: {
              size: 100,
              type: WidthType.PERCENTAGE,
            },
            rows: tableRows,
          })
        );
      });
    }

    // إضافة تاريخ الإنشاء
    children.push(
      new Paragraph({
        children: [
          new TextRun({
            text: `تاريخ إنشاء التقرير: ${new Date().toLocaleDateString('ar-DZ')}`,
            size: 18,
            italics: true,
          }),
        ],
        alignment: AlignmentType.LEFT,
      })
    );

    // إنشاء المستند
    const doc = new Document({
      sections: [
        {
          properties: {},
          children: children,
        },
      ],
    });

    // تحويل إلى buffer وتنزيل
    const buffer = await Packer.toBuffer(doc);
    const blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    });

    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = options.fileName.endsWith('.docx') ? options.fileName : `${options.fileName}.docx`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast.success('تم تصدير ملف Word بنجاح');

  } catch (error) {
    console.error('Error exporting to Word:', error);
    toast.error('حدث خطأ أثناء تصدير ملف Word');
  }
};

/**
 * تصدير بيانات إلى ملف Excel متقدم
 * @param options خيارات التصدير
 */
export const exportToAdvancedExcel = async (options: {
  title: string;
  fileName: string;
  sheets: Array<{
    name: string;
    data: any[][];
    headers?: string[];
    columnWidths?: number[];
  }>;
  schoolInfo?: {
    name: string;
    address?: string;
  };
}) => {
  try {
    // استيراد مكتبة xlsx
    const XLSX = await import('xlsx');

    // إنشاء مصنف Excel جديد
    const workbook = XLSX.utils.book_new();

    // إضافة كل ورقة عمل
    options.sheets.forEach(sheet => {
      let sheetData = [];

      // إضافة معلومات المؤسسة في أول ورقة
      if (options.sheets.indexOf(sheet) === 0 && options.schoolInfo) {
        sheetData.push([options.schoolInfo.name]);
        if (options.schoolInfo.address) {
          sheetData.push([options.schoolInfo.address]);
        }
        sheetData.push([options.title]);
        sheetData.push([]); // سطر فارغ
      }

      // إضافة العناوين إذا كانت موجودة
      if (sheet.headers) {
        sheetData.push(sheet.headers);
      }

      // إضافة البيانات
      sheetData = sheetData.concat(sheet.data);

      // إنشاء ورقة العمل
      const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

      // تعيين عرض الأعمدة إذا كان محدداً
      if (sheet.columnWidths) {
        worksheet['!cols'] = sheet.columnWidths.map(width => ({ wch: width }));
      }

      // إضافة ورقة العمل إلى المصنف
      XLSX.utils.book_append_sheet(workbook, worksheet, sheet.name);
    });

    // تحويل المصنف إلى buffer
    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });

    // إنشاء blob وتنزيل الملف
    const blob = new Blob([excelBuffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });

    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = options.fileName.endsWith('.xlsx') ? options.fileName : `${options.fileName}.xlsx`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast.success('تم تصدير ملف Excel بنجاح');

  } catch (error) {
    console.error('Error exporting to Excel:', error);
    toast.error('حدث خطأ أثناء تصدير ملف Excel');
  }
};
