'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { CalendarIcon, FileText, DollarSign, BarChart3, Users, BookOpen, Activity, TrendingUp, TrendingDown, Eye, Settings, Upload, Plus, Edit, Printer, Trash2, Download } from 'lucide-react';
import { format, subDays, subMonths, startOfMonth, endOfMonth, startOfYear } from 'date-fns';
import { ar } from 'date-fns/locale';
import { cn } from '@/utils/cn';

interface QuickStats {
  students: number;
  teachers: number;
  memorizers: number;
  activities: number;
  income: number;
  expenses: number;
}

interface OfficeSettings {
  organizationName: string;
  officeName: string;
  branchName: string;
  presidentName: string;
  presidentTitle: string;
  logoUrl?: string;
  address?: string;
  phone?: string;
  email?: string;
}

interface SupervisorReport {
  id: number;
  title: string;
  description?: string;
  periodStart: string;
  periodEnd: string;
  status: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  hasLiteraryContent: boolean;
  hasFinancialData: boolean;
}

export default function SupervisorReportsPage() {
  const [startDate, setStartDate] = useState<Date>(startOfMonth(new Date()));
  const [endDate, setEndDate] = useState<Date>(new Date());
  const [quickStats, setQuickStats] = useState<QuickStats>({
    students: 0,
    teachers: 0,
    memorizers: 0,
    activities: 0,
    income: 0,
    expenses: 0,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [officeSettings, setOfficeSettings] = useState<OfficeSettings>({
    organizationName: 'جمعـية العـلمـاء المسلـميـن الجـزائـرييــــــن',
    officeName: 'المكـــــــتب البلدي لبــــلـــــديـــة المنــــقــــر',
    branchName: 'شعبة بلدية المنقر',
    presidentName: 'الوليد بن ناصر قصي',
    presidentTitle: 'رئيس المكتب البلدي',
    logoUrl: '/images/association-logo.svg',
    address: '',
    phone: '',
    email: '',
  });
  const [showSettings, setShowSettings] = useState(false);

  // حالات جديدة للتقارير الموحدة
  const [currentView, setCurrentView] = useState<'dashboard' | 'reports-list' | 'create-report'>('dashboard');
  const [savedReports, setSavedReports] = useState<SupervisorReport[]>([]);
  const [isLoadingReports, setIsLoadingReports] = useState(false);
  const [selectedReport, setSelectedReport] = useState<SupervisorReport | null>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  // تحديث الإحصائيات السريعة عند تغيير التواريخ
  useEffect(() => {
    fetchQuickStats();
  }, [startDate, endDate]);

  // تحميل الإعدادات المحفوظة عند بدء التشغيل
  useEffect(() => {
    const savedSettings = localStorage.getItem('officeSettings');
    if (savedSettings) {
      try {
        const parsed = JSON.parse(savedSettings);
        setOfficeSettings(parsed);
      } catch (error) {
        console.error('خطأ في تحميل الإعدادات المحفوظة:', error);
      }
    }
  }, []);

  // جلب التقارير المحفوظة عند تغيير العرض
  useEffect(() => {
    if (currentView === 'reports-list') {
      fetchSavedReports();
    }
  }, [currentView]);

  const fetchQuickStats = async () => {
    setIsLoading(true);
    try {
      // استخدام API الإحصائيات السريعة الجديد
      const response = await fetch(
        `/api/reports/quick-stats?startDate=${startDate.toISOString()}&endDate=${endDate.toISOString()}`
      );

      if (response.ok) {
        const result = await response.json();
        setQuickStats(result.data);
      } else {
        console.error('فشل في جلب الإحصائيات السريعة');
        // استخدام بيانات افتراضية في حالة الفشل
        setQuickStats({
          students: 250,
          teachers: 15,
          memorizers: 45,
          activities: 25,
          income: 85000,
          expenses: 60000,
        });
      }
    } catch (error) {
      console.error('خطأ في جلب الإحصائيات السريعة:', error);
      // استخدام بيانات افتراضية في حالة الخطأ
      setQuickStats({
        students: 250,
        teachers: 15,
        memorizers: 45,
        activities: 25,
        income: 85000,
        expenses: 60000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  // جلب التقارير المحفوظة
  const fetchSavedReports = async () => {
    setIsLoadingReports(true);
    try {
      const response = await fetch('/api/supervisor-reports');
      if (response.ok) {
        const result = await response.json();
        setSavedReports(result.data.reports || []);
      } else {
        console.error('فشل في جلب التقارير المحفوظة');
      }
    } catch (error) {
      console.error('خطأ في جلب التقارير المحفوظة:', error);
    } finally {
      setIsLoadingReports(false);
    }
  };

  // دوال الفترات السريعة
  const setQuickRange = (type: string) => {
    const today = new Date();
    switch (type) {
      case 'thisMonth':
        setStartDate(startOfMonth(today));
        setEndDate(endOfMonth(today));
        break;
      case 'lastMonth':
        const lastMonth = subMonths(today, 1);
        setStartDate(startOfMonth(lastMonth));
        setEndDate(endOfMonth(lastMonth));
        break;
      case 'thisYear':
        setStartDate(startOfYear(today));
        setEndDate(today);
        break;
      case 'last30Days':
        setStartDate(subDays(today, 30));
        setEndDate(today);
        break;
    }
  };

  const handlePreviewReport = (type: 'literary' | 'financial') => {
    const params = new URLSearchParams({
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      preview: 'true'
    });

    window.open(`/admin/supervisor-reports/${type}?${params}`, '_blank');
  };

  const handleGenerateReport = (type: 'literary' | 'financial') => {
    const params = new URLSearchParams({
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      organizationName: officeSettings.organizationName,
      officeName: officeSettings.officeName,
      branchName: officeSettings.branchName,
      presidentName: officeSettings.presidentName,
      presidentTitle: officeSettings.presidentTitle,
      logoUrl: officeSettings.logoUrl || '',
    });

    window.location.href = `/admin/supervisor-reports/${type}?${params}`;
  };

  const handleOfficeSettingsChange = (field: keyof OfficeSettings, value: string) => {
    setOfficeSettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setOfficeSettings(prev => ({
          ...prev,
          logoUrl: result
        }));
      };
      reader.readAsDataURL(file);
    }
  };

  // دوال التعامل مع التقارير المحفوظة
  const handleViewReport = (report: SupervisorReport) => {
    window.open(`/admin/supervisor-reports/view/${report.id}`, '_blank');
  };

  const handleEditReport = (report: SupervisorReport) => {
    window.location.href = `/admin/supervisor-reports/edit/${report.id}`;
  };

  const handlePrintReport = async (report: SupervisorReport) => {
    try {
      const response = await fetch(`/api/supervisor-reports/${report.id}/export`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ format: 'pdf' }),
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `تقرير_${report.title}_${new Date().toISOString().split('T')[0]}.pdf`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      console.error('خطأ في طباعة التقرير:', error);
    }
  };

  const handleDeleteReport = async (report: SupervisorReport) => {
    try {
      const response = await fetch(`/api/supervisor-reports/${report.id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setSavedReports(prev => prev.filter(r => r.id !== report.id));
        setShowDeleteDialog(false);
        setSelectedReport(null);
      }
    } catch (error) {
      console.error('خطأ في حذف التقرير:', error);
    }
  };

  const handleCreateNewReport = () => {
    window.location.href = '/admin/supervisor-reports/create';
  };

  // عرض مشروط حسب الحالة الحالية
  if (currentView === 'reports-list') {
    return (
      <div className="container mx-auto p-6 space-y-6" dir="rtl">
        {/* رأس صفحة التقارير المحفوظة */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-2">
              <FileText className="h-8 w-8 text-primary" />
              التقارير المحفوظة
            </h1>
            <p className="text-gray-600 mt-1">
              عرض وإدارة التقارير الموحدة المحفوظة
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button
              onClick={handleCreateNewReport}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              إضافة تقرير جديد
            </Button>
            <Button
              variant="outline"
              onClick={() => setCurrentView('dashboard')}
            >
              العودة للوحة الرئيسية
            </Button>
          </div>
        </div>

        {/* جدول التقارير */}
        <Card>
          <CardHeader>
            <CardTitle>قائمة التقارير المحفوظة</CardTitle>
            <CardDescription>
              جميع التقارير الموحدة التي تم إنشاؤها وحفظها
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoadingReports ? (
              <div className="text-center py-8">
                <div className="text-gray-500">جاري تحميل التقارير...</div>
              </div>
            ) : savedReports.length === 0 ? (
              <div className="text-center py-8">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <div className="text-gray-500 mb-4">لا توجد تقارير محفوظة</div>
                <Button onClick={handleCreateNewReport}>
                  إنشاء أول تقرير
                </Button>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="text-right">العنوان</TableHead>
                    <TableHead className="text-right">الفترة الزمنية</TableHead>
                    <TableHead className="text-right">تاريخ الإنشاء</TableHead>
                    <TableHead className="text-right">المنشئ</TableHead>
                    <TableHead className="text-right">الحالة</TableHead>
                    <TableHead className="text-right">الإعدادات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {savedReports.map((report) => (
                    <TableRow key={report.id}>
                      <TableCell className="font-medium">
                        <div>
                          <div className="font-semibold">{report.title}</div>
                          {report.description && (
                            <div className="text-sm text-gray-500 mt-1">
                              {report.description}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div>من: {format(new Date(report.periodStart), 'PPP', { locale: ar })}</div>
                          <div>إلى: {format(new Date(report.periodEnd), 'PPP', { locale: ar })}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {format(new Date(report.createdAt), 'PPP', { locale: ar })}
                      </TableCell>
                      <TableCell>{report.createdBy}</TableCell>
                      <TableCell>
                        <Badge
                          variant={report.status === 'PUBLISHED' ? 'default' : 'secondary'}
                        >
                          {report.status === 'PUBLISHED' ? 'منشور' : 'مسودة'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleViewReport(report)}
                            title="عرض"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditReport(report)}
                            title="تعديل"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handlePrintReport(report)}
                            title="طباعة"
                          >
                            <Printer className="h-4 w-4" />
                          </Button>
                          <AlertDialog open={showDeleteDialog && selectedReport?.id === report.id}>
                            <AlertDialogTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  setSelectedReport(report);
                                  setShowDeleteDialog(true);
                                }}
                                title="حذف"
                                className="text-red-600 hover:text-red-700"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>تأكيد الحذف</AlertDialogTitle>
                                <AlertDialogDescription>
                                  هل أنت متأكد من حذف التقرير "{report.title}"؟
                                  هذا الإجراء لا يمكن التراجع عنه.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel onClick={() => {
                                  setShowDeleteDialog(false);
                                  setSelectedReport(null);
                                }}>
                                  إلغاء
                                </AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => handleDeleteReport(report)}
                                  className="bg-red-600 hover:bg-red-700"
                                >
                                  حذف
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6" dir="rtl">
      {/* الرأس */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-2">
            <BarChart3 className="h-8 w-8 text-primary" />
            التقارير الأدبية والمالية
          </h1>
          <p className="text-gray-600 mt-1">
            إدارة وإنشاء التقارير الأدبية والمالية
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={() => setCurrentView('reports-list')}
            className="flex items-center gap-2"
          >
            <FileText className="h-4 w-4" />
            التقارير المحفوظة
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowSettings(!showSettings)}
          >
            <Settings className="h-4 w-4 mr-2" />
            إعدادات المكتب
          </Button>
          <div className="text-sm text-gray-500">
            📅 {format(new Date(), 'PPP', { locale: ar })}
          </div>
        </div>
      </div>

      {/* إعدادات المكتب البلدي */}
      {showSettings && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              إعدادات المكتب البلدي
            </CardTitle>
            <CardDescription>
              قم بتخصيص معلومات المكتب التي ستظهر في التقارير
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid md:grid-cols-2 gap-6">
              {/* المعلومات الأساسية */}
              <div className="space-y-4">
                <h3 className="font-medium text-gray-900">المعلومات الأساسية</h3>

                <div className="space-y-2">
                  <Label htmlFor="organizationName">اسم الجمعية</Label>
                  <Input
                    id="organizationName"
                    value={officeSettings.organizationName}
                    onChange={(e) => handleOfficeSettingsChange('organizationName', e.target.value)}
                    placeholder="جمعـية العـلمـاء المسلـميـن الجـزائـرييــــــن"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="officeName">اسم المكتب البلدي</Label>
                  <Input
                    id="officeName"
                    value={officeSettings.officeName}
                    onChange={(e) => handleOfficeSettingsChange('officeName', e.target.value)}
                    placeholder="المكـــــــتب البلدي لبــــلـــــديـــة المنــــقــــر"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="branchName">اسم الشعبة</Label>
                  <Input
                    id="branchName"
                    value={officeSettings.branchName}
                    onChange={(e) => handleOfficeSettingsChange('branchName', e.target.value)}
                    placeholder="شعبة بلدية المنقر"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="presidentName">اسم رئيس المكتب</Label>
                  <Input
                    id="presidentName"
                    value={officeSettings.presidentName}
                    onChange={(e) => handleOfficeSettingsChange('presidentName', e.target.value)}
                    placeholder="الوليد بن ناصر قصي"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="presidentTitle">منصب رئيس المكتب</Label>
                  <Input
                    id="presidentTitle"
                    value={officeSettings.presidentTitle}
                    onChange={(e) => handleOfficeSettingsChange('presidentTitle', e.target.value)}
                    placeholder="رئيس المكتب البلدي"
                  />
                </div>
              </div>

              {/* الشعار والمعلومات الإضافية */}
              <div className="space-y-4">
                <h3 className="font-medium text-gray-900">الشعار والمعلومات الإضافية</h3>

                <div className="space-y-2">
                  <Label htmlFor="logo">شعار الجمعية</Label>
                  <div className="flex items-center gap-4">
                    <input
                      type="file"
                      id="logo"
                      accept="image/*"
                      onChange={handleLogoUpload}
                      className="hidden"
                    />
                    <Button
                      variant="outline"
                      onClick={() => document.getElementById('logo')?.click()}
                      className="flex items-center gap-2"
                    >
                      <Upload className="h-4 w-4" />
                      رفع الشعار
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => setOfficeSettings(prev => ({ ...prev, logoUrl: '/images/association-logo.svg' }))}
                      className="flex items-center gap-2"
                    >
                      استعادة الشعار الافتراضي
                    </Button>
                    {officeSettings.logoUrl && (
                      <div className="w-16 h-16 border rounded-lg overflow-hidden">
                        <img
                          src={officeSettings.logoUrl}
                          alt="شعار الجمعية"
                          className="w-full h-full object-contain"
                        />
                      </div>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="address">العنوان</Label>
                  <Textarea
                    id="address"
                    value={officeSettings.address || ''}
                    onChange={(e) => handleOfficeSettingsChange('address', e.target.value)}
                    placeholder="عنوان المكتب البلدي"
                    rows={3}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phone">رقم الهاتف</Label>
                  <Input
                    id="phone"
                    value={officeSettings.phone || ''}
                    onChange={(e) => handleOfficeSettingsChange('phone', e.target.value)}
                    placeholder="+213 XX XX XX XX"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">البريد الإلكتروني</Label>
                  <Input
                    id="email"
                    type="email"
                    value={officeSettings.email || ''}
                    onChange={(e) => handleOfficeSettingsChange('email', e.target.value)}
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>
            </div>

            <div className="flex justify-end gap-2 pt-4 border-t">
              <Button
                variant="outline"
                onClick={() => setShowSettings(false)}
              >
                إلغاء
              </Button>
              <Button
                onClick={() => {
                  localStorage.setItem('officeSettings', JSON.stringify(officeSettings));
                  setShowSettings(false);
                }}
              >
                حفظ الإعدادات
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* شريط التنقل السريع */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CalendarIcon className="h-5 w-5" />
            اختيار الفترة الزمنية
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* اختيار التواريخ */}
          <div className="flex items-center gap-4 flex-wrap">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">من:</span>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-[200px] justify-start text-left font-normal",
                      !startDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {startDate ? format(startDate, "PPP", { locale: ar }) : "اختر التاريخ"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={startDate}
                    onSelect={(date) => date && setStartDate(date)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">إلى:</span>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-[200px] justify-start text-left font-normal",
                      !endDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {endDate ? format(endDate, "PPP", { locale: ar }) : "اختر التاريخ"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={endDate}
                    onSelect={(date) => date && setEndDate(date)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>

          {/* الفترات السريعة */}
          <div className="flex items-center gap-2 flex-wrap">
            <span className="text-sm font-medium">فترات سريعة:</span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setQuickRange('thisMonth')}
            >
              الشهر الحالي
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setQuickRange('lastMonth')}
            >
              الشهر السابق
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setQuickRange('last30Days')}
            >
              آخر 30 يوم
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setQuickRange('thisYear')}
            >
              السنة الحالية
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* الإحصائيات السريعة */}
      <Card>
        <CardHeader>
          <CardTitle>📊 إحصائيات سريعة للفترة المحددة</CardTitle>
          <CardDescription>
            من {format(startDate, 'PPP', { locale: ar })} إلى {format(endDate, 'PPP', { locale: ar })}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <Users className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-blue-900">
                {isLoading ? '...' : quickStats.students?.toLocaleString() || '0'}
              </div>
              <div className="text-sm text-blue-700">الطلاب</div>
            </div>

            <div className="text-center p-4 bg-indigo-50 rounded-lg">
              <Users className="h-8 w-8 text-indigo-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-indigo-900">
                {isLoading ? '...' : quickStats.teachers?.toLocaleString() || '0'}
              </div>
              <div className="text-sm text-indigo-700">المعلمين</div>
            </div>

            <div className="text-center p-4 bg-green-50 rounded-lg">
              <BookOpen className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-green-900">
                {isLoading ? '...' : quickStats.memorizers?.toLocaleString() || '0'}
              </div>
              <div className="text-sm text-green-700">الحفاظ</div>
            </div>

            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <Activity className="h-8 w-8 text-purple-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-purple-900">
                {isLoading ? '...' : quickStats.activities?.toLocaleString() || '0'}
              </div>
              <div className="text-sm text-purple-700">الأنشطة</div>
            </div>

            <div className="text-center p-4 bg-emerald-50 rounded-lg">
              <TrendingUp className="h-8 w-8 text-emerald-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-emerald-900">
                {isLoading ? '...' : quickStats.income?.toLocaleString() || '0'}
              </div>
              <div className="text-sm text-emerald-700">المداخيل</div>
            </div>

            <div className="text-center p-4 bg-red-50 rounded-lg">
              <TrendingDown className="h-8 w-8 text-red-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-red-900">
                {isLoading ? '...' : quickStats.expenses?.toLocaleString() || '0'}
              </div>
              <div className="text-sm text-red-700">المصروفات</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* بطاقات التقارير */}
      <div className="grid md:grid-cols-2 gap-6">
        {/* التقرير الأدبي */}
        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-6 w-6 text-blue-600" />
              التقرير الأدبي
            </CardTitle>
            <CardDescription>
              إحصائيات تعليمية وبيانات الطلاب وتقدم القرآن والأنشطة والفعاليات
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-2 text-sm">
              <Badge variant="secondary" className="justify-center">📊 إحصائيات تعليمية</Badge>
              <Badge variant="secondary" className="justify-center">👥 بيانات الطلاب</Badge>
              <Badge variant="secondary" className="justify-center">📖 تقدم القرآن</Badge>
              <Badge variant="secondary" className="justify-center">🎯 الأنشطة والفعاليات</Badge>
            </div>

            <Separator />

            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePreviewReport('literary')}
                className="flex-1"
              >
                <Eye className="h-4 w-4 mr-2" />
                معاينة
              </Button>
              <Button
                size="sm"
                onClick={() => handleGenerateReport('literary')}
                className="flex-1"
              >
                <FileText className="h-4 w-4 mr-2" />
                إنشاء
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* التقرير المالي */}
        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-6 w-6 text-green-600" />
              التقرير المالي
            </CardTitle>
            <CardDescription>
              مداخيل ومصروفات وتحليل مالي وحالة الخزينة وتفاصيل المعاملات
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-2 text-sm">
              <Badge variant="secondary" className="justify-center">💳 مداخيل ومصروفات</Badge>
              <Badge variant="secondary" className="justify-center">📈 تحليل مالي</Badge>
              <Badge variant="secondary" className="justify-center">🏦 حالة الخزينة</Badge>
              <Badge variant="secondary" className="justify-center">📋 تفاصيل المعاملات</Badge>
            </div>

            <Separator />

            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePreviewReport('financial')}
                className="flex-1"
              >
                <Eye className="h-4 w-4 mr-2" />
                معاينة
              </Button>
              <Button
                size="sm"
                onClick={() => handleGenerateReport('financial')}
                className="flex-1"
              >
                <DollarSign className="h-4 w-4 mr-2" />
                إنشاء
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* معلومات إضافية */}
      <Card>
        <CardHeader>
          <CardTitle>ℹ️ معلومات مهمة</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-2 gap-4 text-sm text-gray-600">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">التقرير الأدبي يشمل:</h4>
              <ul className="space-y-1">
                <li>• إحصائيات الطلاب والمعلمين</li>
                <li>• معدلات الحضور والغياب</li>
                <li>• تقدم حفظ القرآن الكريم</li>
                <li>• الأنشطة والفعاليات المنجزة</li>
                <li>• نتائج الامتحانات والتقييمات</li>
              </ul>
              <div className="mt-3 p-2 bg-blue-50 rounded text-xs">
                <strong>أنواع التصدير:</strong> PDF، Word، طباعة
              </div>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">التقرير المالي يشمل:</h4>
              <ul className="space-y-1">
                <li>• الملخص التنفيذي للوضع المالي</li>
                <li>• تفاصيل المداخيل والمصروفات</li>
                <li>• إحصائيات طرق الدفع</li>
                <li>• التحليل الشهري للمعاملات</li>
                <li>• تقارير فئات المصروفات</li>
              </ul>
              <div className="mt-3 p-2 bg-green-50 rounded text-xs">
                <strong>أنواع التصدير:</strong> PDF، Excel، طباعة
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
