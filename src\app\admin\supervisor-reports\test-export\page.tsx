'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import ReportExporter from '@/components/supervisor-reports/ReportExporter';
import { toast } from 'react-toastify';

export default function TestExportPage() {
  const [isLoading, setIsLoading] = useState(false);

  // بيانات تجريبية للتقرير
  const testReportData = {
    title: 'تقرير تجريبي للاختبار',
    description: 'هذا تقرير تجريبي لاختبار وظائف الطباعة والتصدير',
    periodStart: new Date('2024-01-01'),
    periodEnd: new Date('2024-12-31'),
    literaryContent: `
      <p><strong>بسم الله الرحمن الرحيم</strong></p>

      <h2>التقرير الأدبي للعام الدراسي 2024</h2>

      <p>الحمد لله رب العالمين، والصلاة والسلام على أشرف المرسلين، سيدنا محمد وعلى آله وصحبه أجمعين.</p>

      <p><em>أما بعد:</em></p>

      <p>يسرنا أن نقدم لكم التقرير الأدبي الشامل للعام الدراسي 2024، والذي يتضمن:</p>

      <h3>1. الإنجازات المحققة:</h3>
      <ul>
         <li>تحفيظ القرآن الكريم لعدد <strong>150</strong> طالب وطالبة</li>
         <li>إقامة <span style="color: blue;">12 مسابقة قرآنية</span> على مستوى المنطقة</li>
         <li>تنظيم 8 دورات تدريبية للمعلمين</li>
         <li>افتتاح 3 فصول جديدة لاستيعاب الطلاب الجدد</li>
      </ul>

      <h3>2. الأنشطة التعليمية:</h3>
      <ul>
         <li>الدروس اليومية في التفسير والحديث</li>
         <li>حلقات تحفيظ القرآن الكريم</li>
         <li>دروس في الفقه والعقيدة</li>
         <li>أنشطة ثقافية واجتماعية</li>
      </ul>

      <h3>3. التحديات والصعوبات:</h3>
      <ul>
         <li>نقص في عدد المعلمين المؤهلين</li>
         <li>الحاجة إلى توسيع المرافق</li>
         <li>ضرورة تحديث المناهج التعليمية</li>
      </ul>

      <h3>4. الخطط المستقبلية:</h3>
      <ul>
         <li>زيادة عدد الفصول الدراسية</li>
         <li>تدريب المزيد من المعلمين</li>
         <li>تطوير البرامج التعليمية</li>
         <li>تعزيز الأنشطة اللاصفية</li>
      </ul>

      <p><strong>والله ولي التوفيق</strong></p>
    `,
    financialData: [
      {
        id: '1',
        category: 'المداخيل',
        description: 'رسوم التسجيل',
        amount: 250000,
        date: '2024-01-15',
        type: 'دخل'
      },
      {
        id: '2',
        category: 'المداخيل',
        description: 'تبرعات المحسنين',
        amount: 180000,
        date: '2024-02-10',
        type: 'دخل'
      },
      {
        id: '3',
        category: 'المصروفات',
        description: 'رواتب المعلمين',
        amount: 320000,
        date: '2024-01-30',
        type: 'مصروف'
      },
      {
        id: '4',
        category: 'المصروفات',
        description: 'صيانة المرافق',
        amount: 45000,
        date: '2024-03-05',
        type: 'مصروف'
      },
      {
        id: '5',
        category: 'المصروفات',
        description: 'مواد تعليمية',
        amount: 65000,
        date: '2024-03-15',
        type: 'مصروف'
      }
    ]
  };

  const officeSettings = {
    organizationName: 'جمعـية العـلمـاء المسلـميـن الجـزائـرييــــــن',
    officeName: 'المكـــــــتب البلدي - اختبار',
    presidentName: 'رئيس المكتب البلدي',
    presidentTitle: 'المسؤول عن التقارير'
  };

  // اختبار سريع للطباعة والتصدير
  const handleQuickTest = async () => {
    setIsLoading(true);
    try {
      const { exportToPdf } = await import('@/utils/export-utils');

      exportToPdf({
        title: 'اختبار سريع للتصدير',
        fileName: `اختبار_التصدير_${new Date().toISOString().split('T')[0]}.pdf`,
        tables: [
          {
            title: 'بيانات تجريبية',
            headers: ['البيان', 'القيمة'],
            data: [
              ['اسم التقرير', 'تقرير تجريبي'],
              ['التاريخ', new Date().toLocaleDateString('ar-DZ')],
              ['الحالة', 'اختبار ناجح']
            ],
            headStyles: {
              fillColor: [22, 155, 136],
              textColor: [255, 255, 255]
            }
          }
        ]
      });

      toast.success('تم الاختبار السريع بنجاح!');
    } catch (error) {
      console.error('خطأ في الاختبار:', error);
      toast.error('فشل الاختبار السريع');
    } finally {
      setIsLoading(false);
    }
  };

  // اختبار سريع لتصدير Word
  const handleQuickWordTest = async () => {
    setIsLoading(true);
    try {
      const { Document, Packer, Paragraph, TextRun, HeadingLevel, AlignmentType } = await import('docx');

      const doc = new Document({
        sections: [
          {
            properties: {},
            children: [
              new Paragraph({
                children: [
                  new TextRun({
                    text: 'اختبار تصدير Word',
                    bold: true,
                    size: 32,
                  }),
                ],
                heading: HeadingLevel.TITLE,
                alignment: AlignmentType.CENTER,
              }),
              new Paragraph({
                children: [
                  new TextRun({
                    text: 'هذا اختبار سريع لتصدير ملف Word',
                    size: 24,
                  }),
                ],
                alignment: AlignmentType.CENTER,
              }),
              new Paragraph({
                children: [
                  new TextRun({
                    text: `تاريخ الاختبار: ${new Date().toLocaleDateString('ar-DZ')}`,
                    size: 20,
                  }),
                ],
              }),
            ],
          },
        ],
      });

      const buffer = await Packer.toBuffer(doc);
      const blob = new Blob([buffer], {
        type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      });

      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `اختبار_Word_${new Date().toISOString().split('T')[0]}.docx`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast.success('تم اختبار Word بنجاح!');
    } catch (error) {
      console.error('خطأ في اختبار Word:', error);
      toast.error('فشل اختبار Word');
    } finally {
      setIsLoading(false);
    }
  };

  // اختبار سريع لتصدير Excel
  const handleQuickExcelTest = async () => {
    setIsLoading(true);
    try {
      const XLSX = await import('xlsx');

      // إنشاء مصنف Excel جديد
      const workbook = XLSX.utils.book_new();

      // ورقة الاختبار الأساسية
      const testData = [
        ['اختبار تصدير Excel'],
        [''],
        ['البيان', 'القيمة'],
        ['اسم التقرير', 'تقرير تجريبي Excel'],
        ['التاريخ', new Date().toLocaleDateString('ar-DZ')],
        ['الحالة', 'اختبار ناجح'],
        ['عدد الأوراق', '3'],
      ];

      const testSheet = XLSX.utils.aoa_to_sheet(testData);
      testSheet['!cols'] = [{ wch: 20 }, { wch: 30 }];
      XLSX.utils.book_append_sheet(workbook, testSheet, 'اختبار أساسي');

      // ورقة بيانات مالية تجريبية
      const financialData = [
        ['البيانات المالية التجريبية'],
        [''],
        ['الوصف', 'المبلغ', 'النوع', 'التاريخ'],
        ['رسوم تسجيل', 50000, 'دخل', '2024-01-15'],
        ['تبرعات', 30000, 'دخل', '2024-01-20'],
        ['رواتب', 60000, 'مصروف', '2024-01-30'],
        ['صيانة', 15000, 'مصروف', '2024-02-05'],
      ];

      const financialSheet = XLSX.utils.aoa_to_sheet(financialData);
      financialSheet['!cols'] = [{ wch: 25 }, { wch: 15 }, { wch: 10 }, { wch: 15 }];
      XLSX.utils.book_append_sheet(workbook, financialSheet, 'البيانات المالية');

      // ورقة ملخص
      const summaryData = [
        ['ملخص التقرير'],
        [''],
        ['إجمالي المداخيل', 80000],
        ['إجمالي المصروفات', 75000],
        ['الرصيد الصافي', 5000],
        [''],
        ['ملاحظات'],
        ['هذا اختبار لتصدير Excel'],
        ['جميع البيانات تجريبية'],
      ];

      const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
      summarySheet['!cols'] = [{ wch: 25 }, { wch: 20 }];
      XLSX.utils.book_append_sheet(workbook, summarySheet, 'الملخص');

      // تحويل إلى buffer وتنزيل
      const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
      const blob = new Blob([excelBuffer], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      });

      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `اختبار_Excel_${new Date().toISOString().split('T')[0]}.xlsx`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast.success('تم اختبار Excel بنجاح!');
    } catch (error) {
      console.error('خطأ في اختبار Excel:', error);
      toast.error('فشل اختبار Excel');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl text-center">
            🧪 صفحة اختبار الطباعة والتصدير
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center space-y-4">
            <p className="text-gray-600">
              هذه الصفحة مخصصة لاختبار وظائف الطباعة والتصدير في التقارير الموحدة
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 justify-center">
              <Button
                onClick={handleQuickTest}
                disabled={isLoading}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                {isLoading ? 'جاري الاختبار...' : '🚀 اختبار سريع PDF'}
              </Button>

              <Button
                onClick={handleQuickWordTest}
                disabled={isLoading}
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                {isLoading ? 'جاري الاختبار...' : '📄 اختبار سريع Word'}
              </Button>

              <Button
                onClick={handleQuickExcelTest}
                disabled={isLoading}
                className="bg-emerald-600 hover:bg-emerald-700 text-white"
              >
                {isLoading ? 'جاري الاختبار...' : '📊 اختبار سريع Excel'}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* مكون ReportExporter مع البيانات التجريبية */}
      <ReportExporter
        reportData={testReportData}
        officeSettings={officeSettings}
      />

      {/* معلومات إضافية */}
      <Card>
        <CardHeader>
          <CardTitle>📋 تعليمات الاختبار</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 text-sm">
            <div className="p-3 bg-blue-50 rounded-lg">
              <h4 className="font-semibold text-blue-800">✅ ما يجب أن يعمل:</h4>
              <ul className="list-disc list-inside mt-2 text-blue-700">
                <li>زر "اختبار سريع PDF" يجب أن ينتج ملف PDF</li>
                <li>زر "اختبار سريع Word" يجب أن ينتج ملف DOCX</li>
                <li>زر "اختبار سريع Excel" يجب أن ينتج ملف XLSX</li>
                <li>زر "معاينة" يجب أن يفتح نافذة جديدة مع التقرير</li>
                <li>زر "طباعة" يجب أن يفتح حوار الطباعة</li>
                <li>زر "تصدير HTML" يجب أن ينزل ملف HTML</li>
                <li>زر "تصدير Word" يجب أن ينتج ملف DOCX مفصل</li>
                <li>زر "تصدير Excel" يجب أن ينتج ملف XLSX مفصل</li>
                <li>زر "تصدير PDF" يجب أن ينتج ملف PDF مفصل</li>
              </ul>
            </div>
            
            <div className="p-3 bg-green-50 rounded-lg">
              <h4 className="font-semibold text-green-800">🔧 في حالة وجود مشاكل:</h4>
              <ul className="list-disc list-inside mt-2 text-green-700">
                <li>تحقق من وحدة تحكم المطور (F12) للأخطاء</li>
                <li>تأكد من السماح للنوافذ المنبثقة</li>
                <li>تحقق من أن جميع المكتبات مثبتة بشكل صحيح</li>
              </ul>
            </div>

            <div className="p-3 bg-yellow-50 rounded-lg">
              <h4 className="font-semibold text-yellow-800">✨ ميزة جديدة - تنظيف النص:</h4>
              <ul className="list-disc list-inside mt-2 text-yellow-700">
                <li>المحتوى الأدبي يحتوي على علامات HTML للاختبار</li>
                <li>تصدير Word و Excel سيظهر النص صافياً بدون علامات HTML</li>
                <li>تصدير PDF سيحافظ على التنسيق الأصلي</li>
                <li>تصدير HTML سيحافظ على جميع العلامات والتنسيق</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
