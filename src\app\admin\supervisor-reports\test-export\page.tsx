'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import ReportExporter from '@/components/supervisor-reports/ReportExporter';
import { toast } from 'react-toastify';

export default function TestExportPage() {
  const [isLoading, setIsLoading] = useState(false);

  // بيانات تجريبية للتقرير
  const testReportData = {
    title: 'تقرير تجريبي للاختبار',
    description: 'هذا تقرير تجريبي لاختبار وظائف الطباعة والتصدير',
    periodStart: new Date('2024-01-01'),
    periodEnd: new Date('2024-12-31'),
    literaryContent: `
      بسم الله الرحمن الرحيم

      التقرير الأدبي للعام الدراسي 2024

      الحمد لله رب العالمين، والصلاة والسلام على أشرف المرسلين، سيدنا محمد وعلى آله وصحبه أجمعين.

      أما بعد:

      يسرنا أن نقدم لكم التقرير الأدبي الشامل للعام الدراسي 2024، والذي يتضمن:

      1. الإنجازات المحققة:
         - تحفيظ القرآن الكريم لعدد 150 طالب وطالبة
         - إقامة 12 مسابقة قرآنية على مستوى المنطقة
         - تنظيم 8 دورات تدريبية للمعلمين
         - افتتاح 3 فصول جديدة لاستيعاب الطلاب الجدد

      2. الأنشطة التعليمية:
         - الدروس اليومية في التفسير والحديث
         - حلقات تحفيظ القرآن الكريم
         - دروس في الفقه والعقيدة
         - أنشطة ثقافية واجتماعية

      3. التحديات والصعوبات:
         - نقص في عدد المعلمين المؤهلين
         - الحاجة إلى توسيع المرافق
         - ضرورة تحديث المناهج التعليمية

      4. الخطط المستقبلية:
         - زيادة عدد الفصول الدراسية
         - تدريب المزيد من المعلمين
         - تطوير البرامج التعليمية
         - تعزيز الأنشطة اللاصفية

      والله ولي التوفيق
    `,
    financialData: [
      {
        id: '1',
        category: 'المداخيل',
        description: 'رسوم التسجيل',
        amount: 250000,
        date: '2024-01-15',
        type: 'دخل'
      },
      {
        id: '2',
        category: 'المداخيل',
        description: 'تبرعات المحسنين',
        amount: 180000,
        date: '2024-02-10',
        type: 'دخل'
      },
      {
        id: '3',
        category: 'المصروفات',
        description: 'رواتب المعلمين',
        amount: 320000,
        date: '2024-01-30',
        type: 'مصروف'
      },
      {
        id: '4',
        category: 'المصروفات',
        description: 'صيانة المرافق',
        amount: 45000,
        date: '2024-03-05',
        type: 'مصروف'
      },
      {
        id: '5',
        category: 'المصروفات',
        description: 'مواد تعليمية',
        amount: 65000,
        date: '2024-03-15',
        type: 'مصروف'
      }
    ]
  };

  const officeSettings = {
    organizationName: 'جمعـية العـلمـاء المسلـميـن الجـزائـرييــــــن',
    officeName: 'المكـــــــتب البلدي - اختبار',
    presidentName: 'رئيس المكتب البلدي',
    presidentTitle: 'المسؤول عن التقارير'
  };

  // اختبار سريع للطباعة والتصدير
  const handleQuickTest = async () => {
    setIsLoading(true);
    try {
      const { exportToPdf } = await import('@/utils/export-utils');

      exportToPdf({
        title: 'اختبار سريع للتصدير',
        fileName: `اختبار_التصدير_${new Date().toISOString().split('T')[0]}.pdf`,
        tables: [
          {
            title: 'بيانات تجريبية',
            headers: ['البيان', 'القيمة'],
            data: [
              ['اسم التقرير', 'تقرير تجريبي'],
              ['التاريخ', new Date().toLocaleDateString('ar-DZ')],
              ['الحالة', 'اختبار ناجح']
            ],
            headStyles: {
              fillColor: [22, 155, 136],
              textColor: [255, 255, 255]
            }
          }
        ]
      });

      toast.success('تم الاختبار السريع بنجاح!');
    } catch (error) {
      console.error('خطأ في الاختبار:', error);
      toast.error('فشل الاختبار السريع');
    } finally {
      setIsLoading(false);
    }
  };

  // اختبار سريع لتصدير Word
  const handleQuickWordTest = async () => {
    setIsLoading(true);
    try {
      const { Document, Packer, Paragraph, TextRun, HeadingLevel, AlignmentType } = await import('docx');

      const doc = new Document({
        sections: [
          {
            properties: {},
            children: [
              new Paragraph({
                children: [
                  new TextRun({
                    text: 'اختبار تصدير Word',
                    bold: true,
                    size: 32,
                  }),
                ],
                heading: HeadingLevel.TITLE,
                alignment: AlignmentType.CENTER,
              }),
              new Paragraph({
                children: [
                  new TextRun({
                    text: 'هذا اختبار سريع لتصدير ملف Word',
                    size: 24,
                  }),
                ],
                alignment: AlignmentType.CENTER,
              }),
              new Paragraph({
                children: [
                  new TextRun({
                    text: `تاريخ الاختبار: ${new Date().toLocaleDateString('ar-DZ')}`,
                    size: 20,
                  }),
                ],
              }),
            ],
          },
        ],
      });

      const buffer = await Packer.toBuffer(doc);
      const blob = new Blob([buffer], {
        type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      });

      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `اختبار_Word_${new Date().toISOString().split('T')[0]}.docx`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast.success('تم اختبار Word بنجاح!');
    } catch (error) {
      console.error('خطأ في اختبار Word:', error);
      toast.error('فشل اختبار Word');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl text-center">
            🧪 صفحة اختبار الطباعة والتصدير
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center space-y-4">
            <p className="text-gray-600">
              هذه الصفحة مخصصة لاختبار وظائف الطباعة والتصدير في التقارير الموحدة
            </p>
            
            <div className="flex gap-4 justify-center">
              <Button
                onClick={handleQuickTest}
                disabled={isLoading}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                {isLoading ? 'جاري الاختبار...' : '🚀 اختبار سريع PDF'}
              </Button>

              <Button
                onClick={handleQuickWordTest}
                disabled={isLoading}
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                {isLoading ? 'جاري الاختبار...' : '📄 اختبار سريع Word'}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* مكون ReportExporter مع البيانات التجريبية */}
      <ReportExporter
        reportData={testReportData}
        officeSettings={officeSettings}
      />

      {/* معلومات إضافية */}
      <Card>
        <CardHeader>
          <CardTitle>📋 تعليمات الاختبار</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 text-sm">
            <div className="p-3 bg-blue-50 rounded-lg">
              <h4 className="font-semibold text-blue-800">✅ ما يجب أن يعمل:</h4>
              <ul className="list-disc list-inside mt-2 text-blue-700">
                <li>زر "اختبار سريع PDF" يجب أن ينتج ملف PDF</li>
                <li>زر "اختبار سريع Word" يجب أن ينتج ملف DOCX</li>
                <li>زر "معاينة" يجب أن يفتح نافذة جديدة مع التقرير</li>
                <li>زر "طباعة" يجب أن يفتح حوار الطباعة</li>
                <li>زر "تصدير HTML" يجب أن ينزل ملف HTML</li>
                <li>زر "تصدير Word" يجب أن ينتج ملف DOCX مفصل</li>
                <li>زر "تصدير PDF" يجب أن ينتج ملف PDF مفصل</li>
              </ul>
            </div>
            
            <div className="p-3 bg-green-50 rounded-lg">
              <h4 className="font-semibold text-green-800">🔧 في حالة وجود مشاكل:</h4>
              <ul className="list-disc list-inside mt-2 text-green-700">
                <li>تحقق من وحدة تحكم المطور (F12) للأخطاء</li>
                <li>تأكد من السماح للنوافذ المنبثقة</li>
                <li>تحقق من أن جميع المكتبات مثبتة بشكل صحيح</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
